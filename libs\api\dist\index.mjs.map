{"version": 3, "sources": ["../src/common.ts", "../src/auth.ts", "../src/user.ts", "../src/commune.ts", "../src/reactor.ts", "../src/tag.ts", "../src/rating.ts"], "sourcesContent": ["import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\n\nexport const id = z.string().nanoid();\nexport const idOrNull = id.nullable().default(null);\n\nexport const email = z.string().email();\nexport const query = z.string().nonempty();\n\nexport const createdAt = z.date();\nexport const updatedAt = z.date();\nexport const deletedAt = z.date().nullable();\n\nexport const searchIds = z.array(id).min(1);\n\nexport const stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());\n\nexport function JsonStringToObject<T extends z.ZodRawShape>(schema: T) {\n    return z\n        .string()\n        .transform((value) => JSON.parse(value))\n        .pipe(z.object(schema));\n}\n\nexport function FormDataToObject<T extends z.ZodRawShape>(schema: T) {\n    return z.object({\n        data: JsonStringToObject(schema),\n    });\n}\n\nexport type ObjectWithId = Infer<typeof ObjectWithIdSchema>;\nexport const ObjectWithIdSchema = z.object({ id });\n\nexport type WebsiteLocale = Infer<typeof WebsiteLocaleSchema>;\nexport const WebsiteLocaleSchema = z.enum([\"en\", \"ru\"]);\n\nexport type LocalizationLocale = Infer<typeof LocalizationLocaleSchema>;\nexport const LocalizationLocaleSchema = z.enum([\"en\", \"ru\"]);\n\nexport type LocalizationLocales = Infer<typeof LocalizationLocalesSchema>;\nexport const LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);\n\nexport type Localization = Infer<typeof LocalizationSchema>;\nexport const LocalizationSchema = z.object({\n    locale: LocalizationLocaleSchema,\n    value: z.string().nonempty(),\n});\n\nexport type Localizations = Infer<typeof LocalizationsSchema>;\nexport const LocalizationsSchema = z.array(LocalizationSchema);\n\nexport type Image = Infer<typeof ImageSchema>;\nexport const ImageSchema = z.object({\n    id,\n    url: z.string(),\n    createdAt: stringToDate,\n    updatedAt: stringToDate,\n});\n\nexport type Images = Infer<typeof ImagesSchema>;\nexport const ImagesSchema = z.array(ImageSchema);\n\nexport const pagination = {\n    offset: z.coerce.number()\n        .int()\n        .default(0),\n    limit: z.coerce.number()\n        .int()\n        .positive()\n        .max(100)\n        .default(20),\n\n    page: z.coerce.number()\n        .int()\n        .positive()\n        .default(1),\n    size: z.coerce.number()\n        .int()\n        .positive()\n        .max(100)\n        .default(20),\n};\n\nexport type Pagination = Infer<typeof PaginationSchema>;\nexport const PaginationSchema = z\n    .object({\n        page: pagination.page,\n        size: pagination.size,\n    });\n\nexport function parseInput<T extends z.ZodTypeAny>(\n    schema: T,\n    value: z.input<T>,\n): z.output<T> {\n    return schema.parse(value);\n}\n\nexport function parseUnknown<T extends z.ZodTypeAny>(\n    schema: T,\n    value: unknown,\n): z.output<T> {\n    return schema.parse(value);\n}\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { email, id, ImagesSchema } from \"./common\";\nimport { userDescription, userName, UserRoleSchema } from \"./user\";\n\nexport const otp = z.string().nonempty().length(6);\n\nexport type SendOtpRequest = Infer<typeof SendOtpRequestSchema>;\nexport const SendOtpRequestSchema = z.object({\n    email,\n});\n\nexport type SendOtpResponse = Infer<typeof SendOtpResponseSchema>;\nexport const SendOtpResponseSchema = z.object({\n    isSent: z.boolean(),\n});\n\nexport type GetMeResponse = Infer<typeof GetMeResponseSchema>;\nexport const GetMeResponseSchema = z.object({\n    id,\n    email,\n    role: UserRoleSchema,\n\n    name: userName,\n    description: userDescription,\n\n    images: ImagesSchema,\n\n    createdAt: z.date(),\n    updatedAt: z.date(),\n});\n\nexport type RegisterRequest = Infer<typeof RegisterRequestSchema>;\nexport const RegisterRequestSchema = z.object({\n    referrerId: id.nullable(),\n    email,\n    otp,\n});\n\nexport type LoginRequest = Infer<typeof LoginRequestSchema>;\nexport const LoginRequestSchema = z.object({\n    email,\n    otp,\n});\n\nexport type SuccessfulAuthResponse = Infer<typeof SuccessfulAuthResponseSchema>;\nexport const SuccessfulAuthResponseSchema = z.object({\n    id,\n    email,\n    role: UserRoleSchema,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { email, id, ImagesSchema, LocalizationsSchema } from \"./common\";\n\nexport const userName = LocalizationsSchema;\nexport const userDescription = LocalizationsSchema;\n\nexport const userTitleIsActive = z.boolean();\nexport const userTitleColor = z.string().nonempty().nullable();\n\nexport const userNoteText = z.string().nonempty();\n\nexport type UserRole = Infer<typeof UserRoleSchema>;\nexport const UserRoleSchema = z.enum([\n    \"admin\",\n    \"moderator\",\n    \"user\",\n]);\n\nexport type Author = Infer<typeof AuthorSchema>;\nexport const AuthorSchema = z.object({\n    id,\n    email,\n    name: userName,\n    images: ImagesSchema,\n})\n\nexport type User = Infer<typeof UserSchema>;\nexport const UserSchema = z.object({\n    id,\n    email,\n    role: UserRoleSchema,\n\n    name: userName,\n    description: userDescription,\n\n    images: ImagesSchema,\n\n    createdAt: z.date(),\n    updatedAt: z.date(),\n});\n\nexport type Users = Infer<typeof UsersSchema>;\nexport const UsersSchema = z.array(UserSchema);\n\nexport type UpdateUserRequest = Infer<typeof UpdateUserRequestSchema>;\nexport const UpdateUserRequestSchema = z\n    .object({\n        name: userName,\n        description: userDescription,\n    })\n    .partial();\n\nexport type UserTitle = Infer<typeof UserTitleSchema>;\nexport const UserTitleSchema = z.object({\n    id,\n    ownerId: id.nullable(),\n\n    isActive: userTitleIsActive,\n    color: userTitleColor,\n\n    createdAt: z.date(),\n    updatedAt: z.date(),\n});\n\nexport type UserTitles = Infer<typeof UserTitlesSchema>;\nexport const UserTitlesSchema = z.array(UserTitleSchema);\n\nexport type UpdateUserTitleRequest = Infer<typeof UpdateUserTitleRequestSchema>;\nexport const UpdateUserTitleRequestSchema = z\n    .object({\n        isActive: userTitleIsActive,\n        color: userTitleColor,\n    })\n    .partial();\n\nexport type GetUserNoteResponse = Infer<typeof GetUserNoteResponseSchema>;\nexport const GetUserNoteResponseSchema = z.object({\n    text: userNoteText.nullable(),\n});\n\nexport type SetUserNoteRequest = Infer<typeof SetUserNoteRequestSchema>;\nexport const SetUserNoteRequestSchema = z.object({\n    text: userNoteText.nullable(),\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { id, ImagesSchema, JsonStringToObject, LocalizationsSchema } from \"./common\";\n\nexport type CommuneMemberType = Infer<typeof CommuneMemberTypeSchema>;\nexport const CommuneMemberTypeSchema = z.enum([\"user\"]);\n\nexport const communeMemberActorType = CommuneMemberTypeSchema;\nexport const communeMemberName = LocalizationsSchema;\n\nexport type CommuneMember = Infer<typeof CommuneMemberSchema>;\nexport const CommuneMemberSchema = z.object({\n    id,\n\n    actorType: communeMemberActorType,\n    actorId: id,\n\n    name: communeMemberName,\n\n    images: ImagesSchema,\n\n    joinedAt: z.date(),\n    leftAt: z.date().nullable(),\n});\n\nexport type CommuneMembers = Infer<typeof CommuneMembersSchema>;\nexport const CommuneMembersSchema = z.array(CommuneMemberSchema);\n\nexport const communeName = LocalizationsSchema;\nexport const communeDescription = LocalizationsSchema;\n\nexport type Commune = Infer<typeof CommuneSchema>;\nexport const CommuneSchema = z.object({\n    id,\n\n    name: LocalizationsSchema,\n    description: LocalizationsSchema,\n\n    headMember: z.object({\n        actorType: communeMemberActorType,\n        actorId: id,\n\n        name: communeMemberName,\n    }),\n\n    memberCount: z.number().int().positive(),\n\n    images: ImagesSchema,\n\n    createdAt: z.date(),\n    updatedAt: z.date(),\n});\n\nexport type Communes = Infer<typeof CommunesSchema>;\nexport const CommunesSchema = z.array(CommuneSchema);\n\nexport type CreateCommuneRequest = Infer<typeof CreateCommuneRequestSchema>;\nexport const CreateCommuneRequestSchema = JsonStringToObject({\n    headUserId: id.optional(),\n\n    name: communeName,\n    description: communeDescription,\n\n    // Images are handled separately via file upload\n});\n\nexport type UpdateCommuneRequest = Infer<typeof UpdateCommuneRequestSchema>;\nexport const UpdateCommuneRequestSchema = z.object({\n    name: communeName,\n    description: communeDescription,\n});\n\nexport type CreateCommuneMemberRequest = Infer<typeof CreateCommuneMemberRequestSchema>;\nexport const CreateCommuneMemberRequestSchema = z.object({\n    userId: id,\n});\n\nexport type CommuneInvitationStatus = Infer<typeof CommuneInvitationStatusSchema>;\nexport const CommuneInvitationStatusSchema = z.enum([\"pending\", \"accepted\", \"rejected\", \"expired\"]);\n\nexport type CommuneInvitation = Infer<typeof CommuneInvitationSchema>;\nexport const CommuneInvitationSchema = z.object({\n    id,\n\n    communeId: id,\n    userId: id,\n\n    status: CommuneInvitationStatusSchema,\n\n    createdAt: z.date(),\n    updatedAt: z.date(),\n});\n\nexport type CommuneInvitations = Infer<typeof CommuneInvitationsSchema>;\nexport const CommuneInvitationsSchema = z.array(CommuneInvitationSchema);\n\nexport type CreateCommuneInvitationRequest = Infer<typeof CreateCommuneInvitationRequestSchema>;\nexport const CreateCommuneInvitationRequestSchema = z.object({\n    communeId: id,\n    userId: id,\n});\n\nexport type CommuneJoinRequestStatus = Infer<typeof CommuneJoinRequestStatusSchema>;\nexport const CommuneJoinRequestStatusSchema = z.enum([\"pending\", \"accepted\", \"rejected\"]);\n\nexport type CommuneJoinRequest = Infer<typeof CommuneJoinRequestSchema>;\nexport const CommuneJoinRequestSchema = z.object({\n    id,\n\n    communeId: id,\n    userId: id,\n\n    status: CommuneJoinRequestStatusSchema,\n\n    createdAt: z.date(),\n    updatedAt: z.date(),\n});\n\nexport type CommuneJoinRequests = Infer<typeof CommuneJoinRequestsSchema>;\nexport const CommuneJoinRequestsSchema = z.array(CommuneJoinRequestSchema);\n\nexport type CreateCommuneJoinRequestRequest = Infer<typeof CreateCommuneJoinRequestRequestSchema>;\nexport const CreateCommuneJoinRequestRequestSchema = z.object({\n    communeId: id,\n    userId: id,\n});\n\nexport type TransferHeadStatusRequest = Infer<typeof TransferHeadStatusRequestSchema>;\nexport const TransferHeadStatusRequestSchema = z.object({\n    newHeadUserId: id,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport {\n    createdAt,\n    deletedAt,\n    email,\n    id,\n    ImageSchema,\n    ImagesSchema,\n    LocalizationsSchema,\n    PaginationSchema,\n    query,\n    searchIds,\n    updatedAt,\n} from \"./common\";\nimport { userName } from \"./user\";\nimport { tagName } from \"./tag\";\n\nexport const postUsefulness = z.number().int().min(0).max(10);\n\nexport type Author = Infer<typeof AuthorSchema>;\nexport const AuthorSchema = z.object({\n    id,\n    name: userName,\n    avatar: z.string().nullable(),\n})\n\nexport type RatingType = Infer<typeof RatingTypeSchema>;\nexport const RatingTypeSchema = z.enum([\"like\", \"dislike\"]);\n\nexport type Rating = Infer<typeof RatingSchema>;\nexport const RatingSchema = z.object({\n    likes: z.number().int().nonnegative(),\n    dislikes: z.number().int().nonnegative(),\n    status: RatingTypeSchema.nullable(),\n});\n\nexport type PostUsefulness = Infer<typeof PostUsefulnessSchema>;\nexport const PostUsefulnessSchema = z.object({\n    value: postUsefulness.nullable(),\n    count: z.number().int().nonnegative(),\n    totalValue: z.number().min(0).max(10).nullable(),\n});\n\nexport const postTitle = LocalizationsSchema.min(1);\nexport const postBody = LocalizationsSchema.min(1);\n\nexport const hubName = LocalizationsSchema.min(1);\nexport const hubDescription = LocalizationsSchema.min(1);\n\nexport const communityName = LocalizationsSchema.min(1);\nexport const communityDescription = LocalizationsSchema.min(1);\n\nexport type Post = Infer<typeof PostSchema>;\nexport const PostSchema = z.object({\n    id,\n\n    // hub: z\n    //     .object({\n    //         id,\n    //         name: hubName,\n    //     })\n    //     .nullable(),\n\n    // community: z\n    //     .object({\n    //         id,\n    //         name: communityName,\n    //     })\n    //     .nullable(),\n\n    author: AuthorSchema,\n\n    rating: RatingSchema,\n    usefulness: PostUsefulnessSchema,\n\n    title: postTitle,\n    body: postBody,\n\n    // tags: z.array(\n    //     z.object({\n    //         id,\n    //         name: tagName,\n    //     }),\n    // ),\n    tags: z.array(id),\n\n    createdAt,\n    updatedAt,\n    deletedAt: deletedAt.optional(),\n});\n\nexport type GetPostsRequest = Infer<typeof GetPostsRequestSchema>;\nexport const GetPostsRequestSchema = z.object({\n    paginationSchema: PaginationSchema,\n});\n\nexport type NewAuthor = Infer<typeof NewAuthorSchema>;\nexport const NewAuthorSchema = z.object({\n    id,\n    name: userName,\n    image: z.string().url().nullable(),\n});\n\nexport type GetPostsResponse = Infer<typeof GetPostsResponseSchema>;\nexport const GetPostsResponseSchema = z.object({\n    items: z.array(PostSchema),\n    total: z.number().int().nonnegative(),\n});\n\nexport type GetPost2Response = Infer<typeof GetPost2ResponseSchema>;\nexport const GetPost2ResponseSchema = z.object({\n    id,\n\n    hub: z\n        .object({\n            id,\n            name: hubName,\n        })\n        .nullable(),\n\n    community: z\n        .object({\n            id,\n            name: communityName,\n        })\n        .nullable(),\n\n    author: NewAuthorSchema,\n\n    rating: RatingSchema,\n    usefulness: PostUsefulnessSchema,\n\n    title: postTitle,\n    body: postBody,\n\n    tags: z.array(\n        z.object({\n            id,\n            name: tagName,\n        }),\n    ),\n\n    createdAt,\n    updatedAt,\n    deletedAt: deletedAt.optional(),\n});\n\nexport type GetPosts2Response = Infer<typeof GetPosts2ResponseSchema>;\nexport const GetPosts2ResponseSchema = z.array(GetPost2ResponseSchema);\n\nexport type CreatePost2Request = Infer<typeof CreatePost2RequestSchema>;\nexport const CreatePost2RequestSchema = z.object({\n    hubId: id.nullable(),\n    communityId: id.nullable(),\n    title: postTitle,\n    body: postBody,\n    tags: z.array(id),\n});\n\nexport type CreatePostRequest = Infer<typeof CreatePostRequestSchema>;\nexport const CreatePostRequestSchema = z.object({\n    title: postTitle,\n    body: postBody,\n    tags: z.array(id),\n});\n\nexport type UpdatePostRequest = Infer<typeof UpdatePostRequestSchema>;\nexport const UpdatePostRequestSchema = z\n    .object({\n        title: postTitle,\n        body: postBody,\n        tags: z.array(id),\n    })\n    .partial();\n\nexport type DeletePostRequest = Infer<typeof DeletePostRequestSchema>;\nexport const DeletePostRequestSchema = z.object({\n    reason: z.string().nonempty().nullable(),\n});\n\nexport type UpdatePostRatingRequest = Infer<typeof UpdatePostRatingRequestSchema>;\nexport const UpdatePostRatingRequestSchema = z.object({\n    type: RatingTypeSchema,\n});\n\nexport type UpdatePostRatingResponse = Infer<typeof UpdatePostRatingResponseSchema>;\nexport const UpdatePostRatingResponseSchema = RatingSchema;\n\nexport type UpdatePostUsefulnessRequest = Infer<typeof UpdatePostUsefulnessRequestSchema>;\nexport const UpdatePostUsefulnessRequestSchema = z.object({\n    value: postUsefulness.nullable(),\n});\n\nexport type UpdatePostUsefulnessResponse = Infer<typeof UpdatePostUsefulnessResponseSchema>;\nexport const UpdatePostUsefulnessResponseSchema = PostUsefulnessSchema;\n\nexport type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;\nexport const CommentEntityTypeSchema = z.enum([\"post\", \"comment\"]);\n\nexport const commentBody = LocalizationsSchema.min(1);\n\nexport type Comment = Infer<typeof CommentSchema>;\nexport const CommentSchema = z.object({\n    id,\n\n    path: z.string().nonempty(),\n\n    author: AuthorSchema.nullable(),\n\n    isAnonymous: z.boolean(),\n    anonimityReason: z.string().nonempty().nullable(),\n\n    rating: RatingSchema,\n\n    body: commentBody.nullable(),\n\n    childrenCount: z.number().int().nonnegative(),\n\n    createdAt,\n    updatedAt,\n    deletedAt: deletedAt.optional(),\n\n    deleteReason: z.string().nonempty().nullable(),\n});\n\nexport type GetCommentsRequest = Infer<typeof GetCommentsRequestSchema>;\nexport const GetCommentsRequestSchema = z.object({\n    entityType: CommentEntityTypeSchema,\n    entityId: id,\n});\n\nexport type GetCommentsResponse = Infer<typeof GetCommentsResponseSchema>;\nexport const GetCommentsResponseSchema = z.object({\n    items: z.array(CommentSchema),\n    total: z.number().int().nonnegative(),\n});\n\nexport type CreateCommentRequest = Infer<typeof CreateCommentRequestSchema>;\nexport const CreateCommentRequestSchema = z.object({\n    entityType: CommentEntityTypeSchema,\n    entityId: id,\n\n    body: commentBody,\n});\n\nexport type UpdateCommentRequest = Infer<typeof UpdateCommentRequestSchema>;\nexport const UpdateCommentRequestSchema = z.object({\n    body: commentBody,\n});\n\nexport type DeleteCommentRequest = Infer<typeof DeleteCommentRequestSchema>;\nexport const DeleteCommentRequestSchema = z.object({\n    reason: z.string().nonempty().nullable(),\n});\n\nexport type UpdateCommentRatingRequest = Infer<typeof UpdateCommentRatingRequestSchema>;\nexport const UpdateCommentRatingRequestSchema = z.object({\n    type: RatingTypeSchema,\n});\n\nexport type UpdateCommentRatingResponse = Infer<typeof UpdateCommentRatingResponseSchema>;\nexport const UpdateCommentRatingResponseSchema = RatingSchema;\n\nexport type AnonimifyCommentRequest = Infer<typeof AnonimifyCommentRequestSchema>;\nexport const AnonimifyCommentRequestSchema = z.object({\n    reason: z.string().nonempty().nullable(),\n});\n\nexport type CreateLensRequest = Infer<typeof CreateLensRequestSchema>;\nexport const CreateLensRequestSchema = z.object({\n    name: z.string().nonempty(),\n    code: z.string().nonempty(),\n});\n\nexport type UpdateLensRequest = Infer<typeof UpdateLensRequestSchema>;\nexport const UpdateLensRequestSchema = z\n    .object({\n        name: z.string().nonempty(),\n        code: z.string().nonempty(),\n    })\n    .partial();\n\nexport type GetHubsInput = Infer<typeof GetHubsInputSchema>;\nexport const GetHubsInputSchema = z\n    .object({\n        ids: searchIds,\n        query,\n    })\n    .partial();\n\nexport type GetHubsOutput = Infer<typeof GetHubsOutputSchema>;\nexport const GetHubsOutputSchema = z.array(\n    z.object({\n        id,\n\n        headUser: z.object({\n            id,\n            email,\n            name: userName,\n            images: ImagesSchema,\n        }),\n\n        image: ImageSchema.nullable(),\n\n        name: hubName,\n        description: hubDescription,\n\n        createdAt,\n        updatedAt,\n        deletedAt: deletedAt.optional(),\n    }),\n);\n\nexport type CreateHubInput = Infer<typeof CreateHubInputSchema>;\nexport const CreateHubInputSchema = z.object({\n    headUserId: id,\n\n    name: hubName,\n    description: hubDescription,\n});\n\nexport type UpdateHubInput = Infer<typeof UpdateHubInputSchema>;\nexport const UpdateHubInputSchema = z\n    .object({\n        name: hubName,\n        description: hubDescription,\n    })\n    .partial();\n\nexport type GetCommunitiesInput = Infer<typeof GetCommunitiesInputSchema>;\nexport const GetCommunitiesInputSchema = z\n    .object({\n        ids: searchIds,\n        query,\n\n        hubId: id,\n    })\n    .partial();\n\nexport type GetCommunitiesOutput = Infer<typeof GetCommunitiesOutputSchema>;\nexport const GetCommunitiesOutputSchema = z.array(\n    z.object({\n        id,\n\n        hub: z\n            .object({\n                id,\n                name: hubName,\n                image: ImageSchema.nullable(),\n            })\n            .nullable(),\n\n        headUser: z.object({\n            id,\n            email,\n            name: userName,\n            images: ImagesSchema,\n        }),\n\n        image: ImageSchema.nullable(),\n\n        name: communityName,\n        description: communityDescription,\n\n        createdAt,\n        updatedAt,\n        deletedAt: deletedAt.optional(),\n    }),\n);\n\nexport type CreateCommunityInput = Infer<typeof CreateCommunityInputSchema>;\nexport const CreateCommunityInputSchema = z.object({\n    hubId: id.nullable(),\n    headUserId: id,\n\n    name: communityName,\n    description: communityDescription,\n});\n\nexport type UpdateCommunityInput = Infer<typeof UpdateCommunityInputSchema>;\nexport const UpdateCommunityInputSchema = z\n    .object({\n        name: communityName,\n        description: communityDescription,\n    })\n    .partial();\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { deletedAt, id, LocalizationsSchema, ObjectWithIdSchema, query } from \"./common\";\n\nexport const tagName = LocalizationsSchema.min(1);\n\nexport type GetTagsInput = Infer<typeof GetTagsInputSchema>;\nexport const GetTagsInputSchema = z\n    .object({\n        ids: z.array(id).min(1),\n        query,\n    })\n    .partial();\n\nexport type GetTagsOutput = Infer<typeof GetTagsOutputSchema>;\nexport const GetTagsOutputSchema = z.array(\n    z.object({\n        id,\n        name: tagName,\n        deletedAt: deletedAt.optional(),\n    })\n);\n\nexport type CreateTagInput = Infer<typeof CreateTagInputSchema>;\nexport const CreateTagInputSchema = z.object({\n    name: tagName,\n});\n\nexport type CreateTagOutput = Infer<typeof CreateTagOutputSchema>;\nexport const CreateTagOutputSchema = ObjectWithIdSchema;\n\nexport type UpdateTagInput = Infer<typeof UpdateTagInputSchema>;\nexport const UpdateTagInputSchema = z.object({\n    name: tagName,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { id, LocalizationsSchema } from \"./common\";\nimport { AuthorSchema } from \"./user\";\n\nexport const karmaPointQuantity = z.number().int();\nexport const karmaPointComment = LocalizationsSchema.min(1);\n\nexport type GetKarmaPointsResponse = Infer<typeof GetKarmaPointsResponseSchema>;\nexport const GetKarmaPointsResponseSchema = z.array(\n    z.object({\n        id,\n        author: AuthorSchema,\n        quantity: karmaPointQuantity,\n        comment: karmaPointComment,\n    }),\n);\n\nexport type SpendKarmaPointRequest = Infer<typeof SpendKarmaPointRequestSchema>;\nexport const SpendKarmaPointRequestSchema = z.object({\n    sourceUserId: id,\n    targetUserId: id,\n    quantity: karmaPointQuantity,\n    comment: karmaPointComment,\n});\n\nexport const userFeedbackValue = z.number().int().min(0).max(10);\nexport const userFeedbackText = LocalizationsSchema.min(1);\n\nexport type GetUserFeedbacksResponse = Infer<typeof GetUserFeedbacksResponseSchema>;\nexport const GetUserFeedbacksResponseSchema = z.array(\n    z.object({\n        id,\n        author: AuthorSchema.nullable(),\n        isAnonymous: z.boolean(),\n        value: userFeedbackValue,\n        text: userFeedbackText,\n    }),\n);\n\nexport type CreateUserFeedbackRequest = Infer<typeof CreateUserFeedbackRequestSchema>;\nexport const CreateUserFeedbackRequestSchema = z.object({\n    sourceUserId: id,\n    targetUserId: id,\n    value: userFeedbackValue,\n    isAnonymous: z.boolean(),\n    text: userFeedbackText,\n});\n\nexport type GetUserSummaryResponse = Infer<typeof GetUserSummaryResponseSchema>;\nexport const GetUserSummaryResponseSchema = z.object({\n    rating: z.number().int(),\n    karma: z.number().int(),\n    rate: z.number().min(0).max(10).nullable(),\n});\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,SAAS;AAEX,IAAM,KAAK,EAAE,OAAO,EAAE,OAAO;AAC7B,IAAM,WAAW,GAAG,SAAS,EAAE,QAAQ,IAAI;AAE3C,IAAM,QAAQ,EAAE,OAAO,EAAE,MAAM;AAC/B,IAAM,QAAQ,EAAE,OAAO,EAAE,SAAS;AAElC,IAAM,YAAY,EAAE,KAAK;AACzB,IAAM,YAAY,EAAE,KAAK;AACzB,IAAM,YAAY,EAAE,KAAK,EAAE,SAAS;AAEpC,IAAM,YAAY,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAEnC,IAAM,eAAe,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,KAAK,CAAC;AAErF,SAAS,mBAA4C,QAAW;AACnE,SAAO,EACF,OAAO,EACP,UAAU,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,EACtC,KAAK,EAAE,OAAO,MAAM,CAAC;AAC9B;AAEO,SAAS,iBAA0C,QAAW;AACjE,SAAO,EAAE,OAAO;AAAA,IACZ,MAAM,mBAAmB,MAAM;AAAA,EACnC,CAAC;AACL;AAGO,IAAM,qBAAqB,EAAE,OAAO,EAAE,GAAG,CAAC;AAG1C,IAAM,sBAAsB,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;AAG/C,IAAM,2BAA2B,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;AAGpD,IAAM,4BAA4B,EAAE,MAAM,wBAAwB,EAAE,IAAI,CAAC;AAGzE,IAAM,qBAAqB,EAAE,OAAO;AAAA,EACvC,QAAQ;AAAA,EACR,OAAO,EAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAGM,IAAM,sBAAsB,EAAE,MAAM,kBAAkB;AAGtD,IAAM,cAAc,EAAE,OAAO;AAAA,EAChC;AAAA,EACA,KAAK,EAAE,OAAO;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AACf,CAAC;AAGM,IAAM,eAAe,EAAE,MAAM,WAAW;AAExC,IAAM,aAAa;AAAA,EACtB,QAAQ,EAAE,OAAO,OAAO,EACnB,IAAI,EACJ,QAAQ,CAAC;AAAA,EACd,OAAO,EAAE,OAAO,OAAO,EAClB,IAAI,EACJ,SAAS,EACT,IAAI,GAAG,EACP,QAAQ,EAAE;AAAA,EAEf,MAAM,EAAE,OAAO,OAAO,EACjB,IAAI,EACJ,SAAS,EACT,QAAQ,CAAC;AAAA,EACd,MAAM,EAAE,OAAO,OAAO,EACjB,IAAI,EACJ,SAAS,EACT,IAAI,GAAG,EACP,QAAQ,EAAE;AACnB;AAGO,IAAM,mBAAmB,EAC3B,OAAO;AAAA,EACJ,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AACrB,CAAC;AAEE,SAAS,WACZ,QACA,OACW;AACX,SAAO,OAAO,MAAM,KAAK;AAC7B;AAEO,SAAS,aACZ,QACA,OACW;AACX,SAAO,OAAO,MAAM,KAAK;AAC7B;;;ACvGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAA,UAAS;;;ACFlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAGX,IAAM,WAAW;AACjB,IAAM,kBAAkB;AAExB,IAAM,oBAAoBC,GAAE,QAAQ;AACpC,IAAM,iBAAiBA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAEtD,IAAM,eAAeA,GAAE,OAAO,EAAE,SAAS;AAGzC,IAAM,iBAAiBA,GAAE,KAAK;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,eAAeA,GAAE,OAAO;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,QAAQ;AACZ,CAAC;AAGM,IAAM,aAAaA,GAAE,OAAO;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EAEN,MAAM;AAAA,EACN,aAAa;AAAA,EAEb,QAAQ;AAAA,EAER,WAAWA,GAAE,KAAK;AAAA,EAClB,WAAWA,GAAE,KAAK;AACtB,CAAC;AAGM,IAAM,cAAcA,GAAE,MAAM,UAAU;AAGtC,IAAM,0BAA0BA,GAClC,OAAO;AAAA,EACJ,MAAM;AAAA,EACN,aAAa;AACjB,CAAC,EACA,QAAQ;AAGN,IAAM,kBAAkBA,GAAE,OAAO;AAAA,EACpC;AAAA,EACA,SAAS,GAAG,SAAS;AAAA,EAErB,UAAU;AAAA,EACV,OAAO;AAAA,EAEP,WAAWA,GAAE,KAAK;AAAA,EAClB,WAAWA,GAAE,KAAK;AACtB,CAAC;AAGM,IAAM,mBAAmBA,GAAE,MAAM,eAAe;AAGhD,IAAM,+BAA+BA,GACvC,OAAO;AAAA,EACJ,UAAU;AAAA,EACV,OAAO;AACX,CAAC,EACA,QAAQ;AAGN,IAAM,4BAA4BA,GAAE,OAAO;AAAA,EAC9C,MAAM,aAAa,SAAS;AAChC,CAAC;AAGM,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C,MAAM,aAAa,SAAS;AAChC,CAAC;;;AD/EM,IAAM,MAAMC,GAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;AAG1C,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC;AACJ,CAAC;AAGM,IAAM,wBAAwBA,GAAE,OAAO;AAAA,EAC1C,QAAQA,GAAE,QAAQ;AACtB,CAAC;AAGM,IAAM,sBAAsBA,GAAE,OAAO;AAAA,EACxC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EAEN,MAAM;AAAA,EACN,aAAa;AAAA,EAEb,QAAQ;AAAA,EAER,WAAWA,GAAE,KAAK;AAAA,EAClB,WAAWA,GAAE,KAAK;AACtB,CAAC;AAGM,IAAM,wBAAwBA,GAAE,OAAO;AAAA,EAC1C,YAAY,GAAG,SAAS;AAAA,EACxB;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,qBAAqBA,GAAE,OAAO;AAAA,EACvC;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,+BAA+BA,GAAE,OAAO;AAAA,EACjD;AAAA,EACA;AAAA,EACA,MAAM;AACV,CAAC;;;AEnDD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAIX,IAAM,0BAA0BC,GAAE,KAAK,CAAC,MAAM,CAAC;AAE/C,IAAM,yBAAyB;AAC/B,IAAM,oBAAoB;AAG1B,IAAM,sBAAsBA,GAAE,OAAO;AAAA,EACxC;AAAA,EAEA,WAAW;AAAA,EACX,SAAS;AAAA,EAET,MAAM;AAAA,EAEN,QAAQ;AAAA,EAER,UAAUA,GAAE,KAAK;AAAA,EACjB,QAAQA,GAAE,KAAK,EAAE,SAAS;AAC9B,CAAC;AAGM,IAAM,uBAAuBA,GAAE,MAAM,mBAAmB;AAExD,IAAM,cAAc;AACpB,IAAM,qBAAqB;AAG3B,IAAM,gBAAgBA,GAAE,OAAO;AAAA,EAClC;AAAA,EAEA,MAAM;AAAA,EACN,aAAa;AAAA,EAEb,YAAYA,GAAE,OAAO;AAAA,IACjB,WAAW;AAAA,IACX,SAAS;AAAA,IAET,MAAM;AAAA,EACV,CAAC;AAAA,EAED,aAAaA,GAAE,OAAO,EAAE,IAAI,EAAE,SAAS;AAAA,EAEvC,QAAQ;AAAA,EAER,WAAWA,GAAE,KAAK;AAAA,EAClB,WAAWA,GAAE,KAAK;AACtB,CAAC;AAGM,IAAM,iBAAiBA,GAAE,MAAM,aAAa;AAG5C,IAAM,6BAA6B,mBAAmB;AAAA,EACzD,YAAY,GAAG,SAAS;AAAA,EAExB,MAAM;AAAA,EACN,aAAa;AAAA;AAGjB,CAAC;AAGM,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,MAAM;AAAA,EACN,aAAa;AACjB,CAAC;AAGM,IAAM,mCAAmCA,GAAE,OAAO;AAAA,EACrD,QAAQ;AACZ,CAAC;AAGM,IAAM,gCAAgCA,GAAE,KAAK,CAAC,WAAW,YAAY,YAAY,SAAS,CAAC;AAG3F,IAAM,0BAA0BA,GAAE,OAAO;AAAA,EAC5C;AAAA,EAEA,WAAW;AAAA,EACX,QAAQ;AAAA,EAER,QAAQ;AAAA,EAER,WAAWA,GAAE,KAAK;AAAA,EAClB,WAAWA,GAAE,KAAK;AACtB,CAAC;AAGM,IAAM,2BAA2BA,GAAE,MAAM,uBAAuB;AAGhE,IAAM,uCAAuCA,GAAE,OAAO;AAAA,EACzD,WAAW;AAAA,EACX,QAAQ;AACZ,CAAC;AAGM,IAAM,iCAAiCA,GAAE,KAAK,CAAC,WAAW,YAAY,UAAU,CAAC;AAGjF,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C;AAAA,EAEA,WAAW;AAAA,EACX,QAAQ;AAAA,EAER,QAAQ;AAAA,EAER,WAAWA,GAAE,KAAK;AAAA,EAClB,WAAWA,GAAE,KAAK;AACtB,CAAC;AAGM,IAAM,4BAA4BA,GAAE,MAAM,wBAAwB;AAGlE,IAAM,wCAAwCA,GAAE,OAAO;AAAA,EAC1D,WAAW;AAAA,EACX,QAAQ;AACZ,CAAC;AAGM,IAAM,kCAAkCA,GAAE,OAAO;AAAA,EACpD,eAAe;AACnB,CAAC;;;ACnID;AAAA;AAAA;AAAA,sBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;;;ACFlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAGX,IAAM,UAAU,oBAAoB,IAAI,CAAC;AAGzC,IAAM,qBAAqBC,GAC7B,OAAO;AAAA,EACJ,KAAKA,GAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAAA,EACtB;AACJ,CAAC,EACA,QAAQ;AAGN,IAAM,sBAAsBA,GAAE;AAAA,EACjCA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,MAAM;AACV,CAAC;AAGM,IAAM,wBAAwB;AAG9B,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,MAAM;AACV,CAAC;;;ADhBM,IAAM,iBAAiBC,GAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAGrD,IAAMC,gBAAeD,GAAE,OAAO;AAAA,EACjC;AAAA,EACA,MAAM;AAAA,EACN,QAAQA,GAAE,OAAO,EAAE,SAAS;AAChC,CAAC;AAGM,IAAM,mBAAmBA,GAAE,KAAK,CAAC,QAAQ,SAAS,CAAC;AAGnD,IAAM,eAAeA,GAAE,OAAO;AAAA,EACjC,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EACpC,UAAUA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EACvC,QAAQ,iBAAiB,SAAS;AACtC,CAAC;AAGM,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,OAAO,eAAe,SAAS;AAAA,EAC/B,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EACpC,YAAYA,GAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AACnD,CAAC;AAEM,IAAM,YAAY,oBAAoB,IAAI,CAAC;AAC3C,IAAM,WAAW,oBAAoB,IAAI,CAAC;AAE1C,IAAM,UAAU,oBAAoB,IAAI,CAAC;AACzC,IAAM,iBAAiB,oBAAoB,IAAI,CAAC;AAEhD,IAAM,gBAAgB,oBAAoB,IAAI,CAAC;AAC/C,IAAM,uBAAuB,oBAAoB,IAAI,CAAC;AAGtD,IAAM,aAAaA,GAAE,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,QAAQC;AAAA,EAER,QAAQ;AAAA,EACR,YAAY;AAAA,EAEZ,OAAO;AAAA,EACP,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQN,MAAMD,GAAE,MAAM,EAAE;AAAA,EAEhB;AAAA,EACA;AAAA,EACA,WAAW,UAAU,SAAS;AAClC,CAAC;AAGM,IAAM,wBAAwBA,GAAE,OAAO;AAAA,EAC1C,kBAAkB;AACtB,CAAC;AAGM,IAAM,kBAAkBA,GAAE,OAAO;AAAA,EACpC;AAAA,EACA,MAAM;AAAA,EACN,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,SAAS;AACrC,CAAC;AAGM,IAAM,yBAAyBA,GAAE,OAAO;AAAA,EAC3C,OAAOA,GAAE,MAAM,UAAU;AAAA,EACzB,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AACxC,CAAC;AAGM,IAAM,yBAAyBA,GAAE,OAAO;AAAA,EAC3C;AAAA,EAEA,KAAKA,GACA,OAAO;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACV,CAAC,EACA,SAAS;AAAA,EAEd,WAAWA,GACN,OAAO;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACV,CAAC,EACA,SAAS;AAAA,EAEd,QAAQ;AAAA,EAER,QAAQ;AAAA,EACR,YAAY;AAAA,EAEZ,OAAO;AAAA,EACP,MAAM;AAAA,EAEN,MAAMA,GAAE;AAAA,IACJA,GAAE,OAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AAAA,EAEA;AAAA,EACA;AAAA,EACA,WAAW,UAAU,SAAS;AAClC,CAAC;AAGM,IAAM,0BAA0BA,GAAE,MAAM,sBAAsB;AAG9D,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C,OAAO,GAAG,SAAS;AAAA,EACnB,aAAa,GAAG,SAAS;AAAA,EACzB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAMA,GAAE,MAAM,EAAE;AACpB,CAAC;AAGM,IAAM,0BAA0BA,GAAE,OAAO;AAAA,EAC5C,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAMA,GAAE,MAAM,EAAE;AACpB,CAAC;AAGM,IAAM,0BAA0BA,GAClC,OAAO;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAMA,GAAE,MAAM,EAAE;AACpB,CAAC,EACA,QAAQ;AAGN,IAAM,0BAA0BA,GAAE,OAAO;AAAA,EAC5C,QAAQA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC3C,CAAC;AAGM,IAAM,gCAAgCA,GAAE,OAAO;AAAA,EAClD,MAAM;AACV,CAAC;AAGM,IAAM,iCAAiC;AAGvC,IAAM,oCAAoCA,GAAE,OAAO;AAAA,EACtD,OAAO,eAAe,SAAS;AACnC,CAAC;AAGM,IAAM,qCAAqC;AAG3C,IAAM,0BAA0BA,GAAE,KAAK,CAAC,QAAQ,SAAS,CAAC;AAE1D,IAAM,cAAc,oBAAoB,IAAI,CAAC;AAG7C,IAAM,gBAAgBA,GAAE,OAAO;AAAA,EAClC;AAAA,EAEA,MAAMA,GAAE,OAAO,EAAE,SAAS;AAAA,EAE1B,QAAQC,cAAa,SAAS;AAAA,EAE9B,aAAaD,GAAE,QAAQ;AAAA,EACvB,iBAAiBA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EAEhD,QAAQ;AAAA,EAER,MAAM,YAAY,SAAS;AAAA,EAE3B,eAAeA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EAE5C;AAAA,EACA;AAAA,EACA,WAAW,UAAU,SAAS;AAAA,EAE9B,cAAcA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AACjD,CAAC;AAGM,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C,YAAY;AAAA,EACZ,UAAU;AACd,CAAC;AAGM,IAAM,4BAA4BA,GAAE,OAAO;AAAA,EAC9C,OAAOA,GAAE,MAAM,aAAa;AAAA,EAC5B,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AACxC,CAAC;AAGM,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,YAAY;AAAA,EACZ,UAAU;AAAA,EAEV,MAAM;AACV,CAAC;AAGM,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,MAAM;AACV,CAAC;AAGM,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,QAAQA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC3C,CAAC;AAGM,IAAM,mCAAmCA,GAAE,OAAO;AAAA,EACrD,MAAM;AACV,CAAC;AAGM,IAAM,oCAAoC;AAG1C,IAAM,gCAAgCA,GAAE,OAAO;AAAA,EAClD,QAAQA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC3C,CAAC;AAGM,IAAM,0BAA0BA,GAAE,OAAO;AAAA,EAC5C,MAAMA,GAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,MAAMA,GAAE,OAAO,EAAE,SAAS;AAC9B,CAAC;AAGM,IAAM,0BAA0BA,GAClC,OAAO;AAAA,EACJ,MAAMA,GAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,MAAMA,GAAE,OAAO,EAAE,SAAS;AAC9B,CAAC,EACA,QAAQ;AAGN,IAAM,qBAAqBA,GAC7B,OAAO;AAAA,EACJ,KAAK;AAAA,EACL;AACJ,CAAC,EACA,QAAQ;AAGN,IAAM,sBAAsBA,GAAE;AAAA,EACjCA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,UAAUA,GAAE,OAAO;AAAA,MACf;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,QAAQ;AAAA,IACZ,CAAC;AAAA,IAED,OAAO,YAAY,SAAS;AAAA,IAE5B,MAAM;AAAA,IACN,aAAa;AAAA,IAEb;AAAA,IACA;AAAA,IACA,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,YAAY;AAAA,EAEZ,MAAM;AAAA,EACN,aAAa;AACjB,CAAC;AAGM,IAAM,uBAAuBA,GAC/B,OAAO;AAAA,EACJ,MAAM;AAAA,EACN,aAAa;AACjB,CAAC,EACA,QAAQ;AAGN,IAAM,4BAA4BA,GACpC,OAAO;AAAA,EACJ,KAAK;AAAA,EACL;AAAA,EAEA,OAAO;AACX,CAAC,EACA,QAAQ;AAGN,IAAM,6BAA6BA,GAAE;AAAA,EACxCA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,KAAKA,GACA,OAAO;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,MACN,OAAO,YAAY,SAAS;AAAA,IAChC,CAAC,EACA,SAAS;AAAA,IAEd,UAAUA,GAAE,OAAO;AAAA,MACf;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,QAAQ;AAAA,IACZ,CAAC;AAAA,IAED,OAAO,YAAY,SAAS;AAAA,IAE5B,MAAM;AAAA,IACN,aAAa;AAAA,IAEb;AAAA,IACA;AAAA,IACA,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,OAAO,GAAG,SAAS;AAAA,EACnB,YAAY;AAAA,EAEZ,MAAM;AAAA,EACN,aAAa;AACjB,CAAC;AAGM,IAAM,6BAA6BA,GACrC,OAAO;AAAA,EACJ,MAAM;AAAA,EACN,aAAa;AACjB,CAAC,EACA,QAAQ;;;AEnYb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAE,UAAS;AAIX,IAAM,qBAAqBC,GAAE,OAAO,EAAE,IAAI;AAC1C,IAAM,oBAAoB,oBAAoB,IAAI,CAAC;AAGnD,IAAM,+BAA+BA,GAAE;AAAA,EAC1CA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,EACb,CAAC;AACL;AAGO,IAAM,+BAA+BA,GAAE,OAAO;AAAA,EACjD,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AACb,CAAC;AAEM,IAAM,oBAAoBA,GAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AACxD,IAAM,mBAAmB,oBAAoB,IAAI,CAAC;AAGlD,IAAM,iCAAiCA,GAAE;AAAA,EAC5CA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,QAAQ,aAAa,SAAS;AAAA,IAC9B,aAAaA,GAAE,QAAQ;AAAA,IACvB,OAAO;AAAA,IACP,MAAM;AAAA,EACV,CAAC;AACL;AAGO,IAAM,kCAAkCA,GAAE,OAAO;AAAA,EACpD,cAAc;AAAA,EACd,cAAc;AAAA,EACd,OAAO;AAAA,EACP,aAAaA,GAAE,QAAQ;AAAA,EACvB,MAAM;AACV,CAAC;AAGM,IAAM,+BAA+BA,GAAE,OAAO;AAAA,EACjD,QAAQA,GAAE,OAAO,EAAE,IAAI;AAAA,EACvB,OAAOA,GAAE,OAAO,EAAE,IAAI;AAAA,EACtB,MAAMA,GAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AAC7C,CAAC;", "names": ["z", "z", "z", "z", "z", "z", "AuthorSchema", "z", "z", "z", "z", "AuthorSchema", "z", "z"]}