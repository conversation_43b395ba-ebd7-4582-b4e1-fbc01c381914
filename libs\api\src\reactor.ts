import type { Infer } from "./types";

import { z } from "zod";
import {
    createdAt,
    deletedAt,
    email,
    id,
    ImageSchema,
    ImagesSchema,
    LocalizationsSchema,
    PaginationSchema,
    query,
    searchIds,
    updatedAt,
} from "./common";
import { userName } from "./user";
import { tagName } from "./tag";

export const postUsefulness = z.number().int().min(0).max(10);

export type Author = Infer<typeof AuthorSchema>;
export const AuthorSchema = z.object({
    id,
    name: userName,
    avatar: z.string().nullable(),
})

export type RatingType = Infer<typeof RatingTypeSchema>;
export const RatingTypeSchema = z.enum(["like", "dislike"]);

export type Rating = Infer<typeof RatingSchema>;
export const RatingSchema = z.object({
    likes: z.number().int().nonnegative(),
    dislikes: z.number().int().nonnegative(),
    status: RatingTypeSchema.nullable(),
});

export type PostUsefulness = Infer<typeof PostUsefulnessSchema>;
export const PostUsefulnessSchema = z.object({
    value: postUsefulness.nullable(),
    count: z.number().int().nonnegative(),
    totalValue: z.number().min(0).max(10).nullable(),
});

export const postTitle = LocalizationsSchema.min(1);
export const postBody = LocalizationsSchema.min(1);

export const hubName = LocalizationsSchema.min(1);
export const hubDescription = LocalizationsSchema.min(1);

export const communityName = LocalizationsSchema.min(1);
export const communityDescription = LocalizationsSchema.min(1);

export type Post = Infer<typeof PostSchema>;
export const PostSchema = z.object({
    id,

    // hub: z
    //     .object({
    //         id,
    //         name: hubName,
    //     })
    //     .nullable(),

    // community: z
    //     .object({
    //         id,
    //         name: communityName,
    //     })
    //     .nullable(),

    author: AuthorSchema,

    rating: RatingSchema,
    usefulness: PostUsefulnessSchema,

    title: postTitle,
    body: postBody,

    // tags: z.array(
    //     z.object({
    //         id,
    //         name: tagName,
    //     }),
    // ),
    tags: z.array(id),

    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional(),
});

export type GetPostsRequest = Infer<typeof GetPostsRequestSchema>;
export const GetPostsRequestSchema = z.object({
    paginationSchema: PaginationSchema,
});

export type NewAuthor = Infer<typeof NewAuthorSchema>;
export const NewAuthorSchema = z.object({
    id,
    name: userName,
    image: z.string().url().nullable(),
});

export type GetPostsResponse = Infer<typeof GetPostsResponseSchema>;
export const GetPostsResponseSchema = z.object({
    items: z.array(PostSchema),
    total: z.number().int().nonnegative(),
});

export type GetPost2Response = Infer<typeof GetPost2ResponseSchema>;
export const GetPost2ResponseSchema = z.object({
    id,

    hub: z
        .object({
            id,
            name: hubName,
        })
        .nullable(),

    community: z
        .object({
            id,
            name: communityName,
        })
        .nullable(),

    author: NewAuthorSchema,

    rating: RatingSchema,
    usefulness: PostUsefulnessSchema,

    title: postTitle,
    body: postBody,

    tags: z.array(
        z.object({
            id,
            name: tagName,
        }),
    ),

    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional(),
});

export type GetPosts2Response = Infer<typeof GetPosts2ResponseSchema>;
export const GetPosts2ResponseSchema = z.array(GetPost2ResponseSchema);

export type CreatePost2Request = Infer<typeof CreatePost2RequestSchema>;
export const CreatePost2RequestSchema = z.object({
    hubId: id.nullable(),
    communityId: id.nullable(),
    title: postTitle,
    body: postBody,
    tags: z.array(id),
});

export type CreatePostRequest = Infer<typeof CreatePostRequestSchema>;
export const CreatePostRequestSchema = z.object({
    title: postTitle,
    body: postBody,
    tags: z.array(id),
});

export type UpdatePostRequest = Infer<typeof UpdatePostRequestSchema>;
export const UpdatePostRequestSchema = z
    .object({
        title: postTitle,
        body: postBody,
        tags: z.array(id),
    })
    .partial();

export type DeletePostRequest = Infer<typeof DeletePostRequestSchema>;
export const DeletePostRequestSchema = z.object({
    reason: z.string().nonempty().nullable(),
});

export type UpdatePostRatingRequest = Infer<typeof UpdatePostRatingRequestSchema>;
export const UpdatePostRatingRequestSchema = z.object({
    type: RatingTypeSchema,
});

export type UpdatePostRatingResponse = Infer<typeof UpdatePostRatingResponseSchema>;
export const UpdatePostRatingResponseSchema = RatingSchema;

export type UpdatePostUsefulnessRequest = Infer<typeof UpdatePostUsefulnessRequestSchema>;
export const UpdatePostUsefulnessRequestSchema = z.object({
    value: postUsefulness.nullable(),
});

export type UpdatePostUsefulnessResponse = Infer<typeof UpdatePostUsefulnessResponseSchema>;
export const UpdatePostUsefulnessResponseSchema = PostUsefulnessSchema;

export type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;
export const CommentEntityTypeSchema = z.enum(["post", "comment"]);

export const commentBody = LocalizationsSchema.min(1);

export type Comment = Infer<typeof CommentSchema>;
export const CommentSchema = z.object({
    id,

    path: z.string().nonempty(),

    author: AuthorSchema.nullable(),

    isAnonymous: z.boolean(),
    anonimityReason: z.string().nonempty().nullable(),

    rating: RatingSchema,

    body: commentBody.nullable(),

    childrenCount: z.number().int().nonnegative(),

    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional(),

    deleteReason: z.string().nonempty().nullable(),
});

export type GetCommentsRequest = Infer<typeof GetCommentsRequestSchema>;
export const GetCommentsRequestSchema = z.object({
    entityType: CommentEntityTypeSchema,
    entityId: id,
});

export type GetCommentsResponse = Infer<typeof GetCommentsResponseSchema>;
export const GetCommentsResponseSchema = z.object({
    items: z.array(CommentSchema),
    total: z.number().int().nonnegative(),
});

export type CreateCommentRequest = Infer<typeof CreateCommentRequestSchema>;
export const CreateCommentRequestSchema = z.object({
    entityType: CommentEntityTypeSchema,
    entityId: id,

    body: commentBody,
});

export type UpdateCommentRequest = Infer<typeof UpdateCommentRequestSchema>;
export const UpdateCommentRequestSchema = z.object({
    body: commentBody,
});

export type DeleteCommentRequest = Infer<typeof DeleteCommentRequestSchema>;
export const DeleteCommentRequestSchema = z.object({
    reason: z.string().nonempty().nullable(),
});

export type UpdateCommentRatingRequest = Infer<typeof UpdateCommentRatingRequestSchema>;
export const UpdateCommentRatingRequestSchema = z.object({
    type: RatingTypeSchema,
});

export type UpdateCommentRatingResponse = Infer<typeof UpdateCommentRatingResponseSchema>;
export const UpdateCommentRatingResponseSchema = RatingSchema;

export type AnonimifyCommentRequest = Infer<typeof AnonimifyCommentRequestSchema>;
export const AnonimifyCommentRequestSchema = z.object({
    reason: z.string().nonempty().nullable(),
});

export type CreateLensRequest = Infer<typeof CreateLensRequestSchema>;
export const CreateLensRequestSchema = z.object({
    name: z.string().nonempty(),
    code: z.string().nonempty(),
});

export type UpdateLensRequest = Infer<typeof UpdateLensRequestSchema>;
export const UpdateLensRequestSchema = z
    .object({
        name: z.string().nonempty(),
        code: z.string().nonempty(),
    })
    .partial();

export type GetHubsInput = Infer<typeof GetHubsInputSchema>;
export const GetHubsInputSchema = z
    .object({
        ids: searchIds,
        query,
    })
    .partial();

export type GetHubsOutput = Infer<typeof GetHubsOutputSchema>;
export const GetHubsOutputSchema = z.array(
    z.object({
        id,

        headUser: z.object({
            id,
            email,
            name: userName,
            images: ImagesSchema,
        }),

        image: ImageSchema.nullable(),

        name: hubName,
        description: hubDescription,

        createdAt,
        updatedAt,
        deletedAt: deletedAt.optional(),
    }),
);

export type CreateHubInput = Infer<typeof CreateHubInputSchema>;
export const CreateHubInputSchema = z.object({
    headUserId: id,

    name: hubName,
    description: hubDescription,
});

export type UpdateHubInput = Infer<typeof UpdateHubInputSchema>;
export const UpdateHubInputSchema = z
    .object({
        name: hubName,
        description: hubDescription,
    })
    .partial();

export type GetCommunitiesInput = Infer<typeof GetCommunitiesInputSchema>;
export const GetCommunitiesInputSchema = z
    .object({
        ids: searchIds,
        query,

        hubId: id,
    })
    .partial();

export type GetCommunitiesOutput = Infer<typeof GetCommunitiesOutputSchema>;
export const GetCommunitiesOutputSchema = z.array(
    z.object({
        id,

        hub: z
            .object({
                id,
                name: hubName,
                image: ImageSchema.nullable(),
            })
            .nullable(),

        headUser: z.object({
            id,
            email,
            name: userName,
            images: ImagesSchema,
        }),

        image: ImageSchema.nullable(),

        name: communityName,
        description: communityDescription,

        createdAt,
        updatedAt,
        deletedAt: deletedAt.optional(),
    }),
);

export type CreateCommunityInput = Infer<typeof CreateCommunityInputSchema>;
export const CreateCommunityInputSchema = z.object({
    hubId: id.nullable(),
    headUserId: id,

    name: communityName,
    description: communityDescription,
});

export type UpdateCommunityInput = Infer<typeof UpdateCommunityInputSchema>;
export const UpdateCommunityInputSchema = z
    .object({
        name: communityName,
        description: communityDescription,
    })
    .partial();
