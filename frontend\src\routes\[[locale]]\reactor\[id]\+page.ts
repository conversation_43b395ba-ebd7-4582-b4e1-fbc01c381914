import type { PageLoad } from './$types';
import type { CommentEntity } from './types';

import { error } from '@sveltejs/kit';
import { fixResponseJsonDates, fixResponseJsonDatesForArray, handleUnauthorized } from "$lib";
import type { Reactor } from '@commune/api';

export const load: PageLoad = async ({ fetch, params, url }) => {
  const [
    postResponse,
    commentsResponse,
  ] = await Promise.all([
    fetch(`/api/reactor/post/${params.id}`),
    fetch(`/api/reactor/comment?entityType=post&entityId=${params.id}`),
  ]);

  handleUnauthorized(postResponse, url);
  handleUnauthorized(commentsResponse, url);

  if (postResponse.status === 404) {
    throw error(404, {
      message: "Post not found",
    });
  }

  if (!postResponse.ok) {
    throw error(500, {
      message: `Failed to fetch post: ${postResponse.statusText}`,
    });
  }

  const post: Reactor.GetPost2Response = await postResponse.json().then(fixResponseJsonDates);

  let comments: CommentEntity[] = [];
  if (commentsResponse.ok) {
    const commentsData = await commentsResponse.json() as {
      items: CommentEntity[];
      total: number;
    };

    comments = fixResponseJsonDatesForArray(commentsData.items);
  }

  return {
    post,
    comments,
  };
};
