import type { Request } from "express";

import util from "util";
import { z } from "zod";
import superjson from "superjson";
import express, { Router } from "express";
import { CaseTransformer, UnknownCaseStrategy, KebabCaseStrategy } from "@ocelotjungle/case-converters";

function log(...args: any[]) {
    console.log(...args);
}

function dir(...args: any[]) {
    console.log(...args.map(arg => util.inspect(arg, { depth: null, colors: true })));
}

type MaybePromise<T> = T | Promise<T>;

const kebabTransformer = new CaseTransformer(
    new UnknownCaseStrategy(),
    new KebabCaseStrategy(),
);

export const methods = [
    "get",
    "post",
    "put",
    "patch",
    "delete",
] as const;

export type Method = typeof methods[number];

/**
 * input = schema => input is validated against the schema
 * 
 * input = null => input is nothing implicitly
 * 
 * input = undefined => validation is disabled, input can be anything
 * 
 * output = schema => output is validated against the schema
 * 
 * output = null | undefined => validation is disabled, output can be anything
 */
type SchemaEndpoint = {
    input: z.ZodType | null | undefined;
    output: z.ZodType | null | undefined;
    /** @default true */
    requireMetadata?: boolean;
    cacheControl?: string;
    /** @default true */
    enableAutoScopeInvalidation?: boolean;
    invalidate?: string[];
};

type SchemaEndpoint2 = Partial<Record<Method, SchemaEndpoint>>;

export interface Schema extends Record<string, Schema | SchemaEndpoint2> { }

export type Handlers<TSchema extends Schema, TMetadata = never> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaEndpoint2
        ? {
            [M in keyof TSchema[K]]: TSchema[K][M] extends SchemaEndpoint
                ? (
                    input: TSchema[K][M]["input"] extends z.ZodType
                        ? z.infer<TSchema[K][M]["input"]>
                        : TSchema[K][M]["input"] extends null
                            ? never
                            : unknown,
                    metadata: TMetadata,
                ) => MaybePromise<
                    TSchema[K][M]["output"] extends z.ZodType
                        ? z.infer<TSchema[K][M]["output"]>
                        : unknown
                >
                : never; 
        }
        : TSchema[K] extends Schema
            ? Handlers<TSchema[K], TMetadata>
            : never;
};

export type Transformer = {
    serialize: (data: any) => string;
    deserialize: (data: string) => any;
}

export const jsonTransformer: Transformer = {
    serialize: JSON.stringify,
    deserialize: JSON.parse,
}

export const superjsonTransformer: Transformer = {
    serialize: superjson.stringify,
    deserialize: superjson.parse,
};

function isEndpoint(schemaEntry: unknown): schemaEntry is SchemaEndpoint {
    return (
        schemaEntry != null && typeof schemaEntry === "object" && (
            (
                "input" in schemaEntry
                && (
                    schemaEntry["input"] instanceof z.ZodType
                    || schemaEntry["input"] === null
                    || schemaEntry["input"] === undefined
                )
            )
            && (
                "output" in schemaEntry
                && (
                    schemaEntry["output"] instanceof z.ZodType
                    || schemaEntry["output"] === null
                    || schemaEntry["output"] === undefined
                )
            )
        )
    );
}

type InputParseResult =
    | { success: true, data: unknown }
    | { success: false, status: number, response: unknown };

function getInput(
    req: Request,
    inputSchema: z.ZodType | null | undefined,
    transformer: Transformer,
): InputParseResult {
    let body: unknown = null;

    dir({
        method: req.method,
        query: req.query,
        body: req.body,
    });

    if (req.method === "GET") {
        body = req.query.__body
            ? decodeURIComponent(req.query.__body as string)
            : null;

        log("get", { body });

        if (inputSchema && !body) {
            return {
                success: false,
                status: 400,
                response: {
                    error: "No __body provided",
                },
            };
        }
    }
    else {
        body = req.body;

        log("non-get", { body });

        if (inputSchema && !body) {
            return {
                success: false,
                status: 400,
                response: {
                    error: "No body provided",
                },
            };
        }
    }

    log("before deserialize", { body });

    const rawInput = body ? transformer.deserialize(body as string) : null;

    dir({ rawInput });

    if (inputSchema) {
        const schemaParseResult = inputSchema.safeParse(rawInput);

        dir({ schemaParseResult });

        if (schemaParseResult.success) {
            return {
                success: true,
                data: schemaParseResult.data,
            };
        }

        return {
            success: false,
            status: 400,
            response: schemaParseResult.error.issues.map(issue => ({
                path: issue.path,
                message: issue.message,
            })),
        };
    }

    return {
        success: true,
        data: rawInput,
    };
}

export function createServer<
    TSchema extends Schema,
    TMetadata extends Record<string, any> = never,
>(
    schema: TSchema,
    handlers: Handlers<TSchema, TMetadata>,
    options?: {
        transformer?: Transformer;
        getMetadata?: (req: Request) => TMetadata;
    },
) {
    const transformer = options?.transformer ?? jsonTransformer;
    const getMetadata = options?.getMetadata;
    const router = Router();

    function fillRouter(
        schema: Record<string, any>,
        handlers: Record<string, any>,
        names: readonly string[],
    ) {
        log({ names });
    
        for (const [name, schemaEntry] of Object.entries(schema) as [string, any][]) {
            log({ name });
    
            if (isEndpoint(schemaEntry)) {
                const path = ["", ...names].join("/");
                const method = name as Method;
                const okStatus = method === "post" ? 201 : 200;
    
                log(`Registering ${method.toUpperCase()} ${path}...`);
    
                if (method !== "get") {
                    router[method](path, express.text());
                }
    
                router[method](
                    path,
                    async (req, res) => {
                        const metadata = getMetadata?.(req);
                        const requireMetadata = schemaEntry.requireMetadata ?? true;

                        dir({
                            getMetadata,
                            metadata,
                            requireMetadata,
                        });

                        if (getMetadata && requireMetadata && !metadata) {
                            return res.status(400).json({
                                error: "Metadata cannot be parsed.",
                            });
                        }

                        const inputParseResult = getInput(
                            req,
                            schemaEntry.input,
                            transformer,
                        );
    
                        dir({ inputParseResult });
    
                        if (!inputParseResult.success) {
                            return res
                                .status(inputParseResult.status)
                                .json(inputParseResult.response);
                        }
    
                        dir({ handlers });
    
                        const rawOutput = await (handlers[name] as any)(
                            inputParseResult.data,
                            metadata ?? undefined,
                        );

                        const outputParseResult = (schemaEntry.output as z.ZodType).safeParse(rawOutput);
    
                        dir({
                            rawOutput,
                            outputParseResult,
                        });
    
                        if (outputParseResult.error) {
                            return res.status(500).json(outputParseResult.error);
                        }
    
                        const serializedOutput = transformer.serialize(outputParseResult.data);
    
                        dir({ serializedOutput });
    
                        return res.status(okStatus).send(serializedOutput);
                    },
                );
            }
            else {
                fillRouter(
                    schema[name] as Schema,
                    handlers[name],
                    [...names, kebabTransformer.transform(name)],
                );
            }
        }
    }

    fillRouter(schema, handlers, []);

    return {
        router,
    }
}

type ClientFetcherInit = RequestInit & {
    fetch?: typeof fetch;
};

export type ClientFetcher<TSchema extends Schema> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaEndpoint2
        ? {
            [M in keyof TSchema[K]]: TSchema[K][M] extends SchemaEndpoint
                ? TSchema[K][M]["input"] extends null
                    ? (init?: ClientFetcherInit) => Promise<
                        TSchema[K][M]["output"] extends z.ZodType
                            ? z.infer<TSchema[K][M]["output"]>
                            : unknown
                    >
                    : TSchema[K][M]["input"] extends z.ZodType
                        ? (input: z.infer<TSchema[K][M]["input"]>, init?: ClientFetcherInit) => Promise<
                            TSchema[K][M]["output"] extends z.ZodType
                                ? z.infer<TSchema[K][M]["output"]>
                                : unknown
                        >
                        : (input?: unknown, init?: ClientFetcherInit) => Promise<
                            TSchema[K][M]["output"] extends z.ZodType
                                ? z.infer<TSchema[K][M]["output"]>
                                : unknown
                        >
                : never;
        }
        : TSchema[K] extends Schema
            ? ClientFetcher<TSchema[K]>
            : never;
};

export class HttpError extends Error {
    status: number;
    description: string;

    constructor(
        status: number,
        description: string,
    ) {
        super(`Fetch failed with status ${status}`);

        this.status = status;
        this.description = description;
    }
}

function initCacheVersionMapEntryFactory(map: Map<string, number>) {
    const now = Date.now();

    return function initCacheVersionMapEntry(path: string) {
        const localStorageKey = `acrpc:cache:${path}`;
        const version = global.localStorage?.getItem(localStorageKey);
    
        dir(
            "initializing cache version map entry",
            {
                map,
                path,
                localStorageKey,
                version
            }
        );
    
        if (version) {
            map.set(path, parseInt(version));
        }
        else {
            global.localStorage?.setItem(localStorageKey, now.toString());
            map.set(path, now);
        }
    
        dir("after", { map });
    }
}

function updateLocalStorageCacheVersion(
    map: Map<string, number>,
    path: string,
) {
    const localStorageKey = `acrpc:cache:${path}`;

    const now = Date.now();

    global.localStorage?.setItem(localStorageKey, now.toString());
    map.set(path, now);
}

export function createClient<TSchema extends Schema>(
    schema: TSchema,
    options: {
        entrypointUrl: string;
        transformer?: Transformer;
        init?: RequestInit;
        fetch?: typeof fetch;
        interceptor?: (ctx: {
            method: Method;
            path: string;
            response: Response;
        }) => MaybePromise<void>;
    },
) {
    const transformer = options.transformer ?? jsonTransformer;
    const url = options.entrypointUrl;

    const cacheVersionMap = new Map<string, number>();
    const initCacheVersionMapEntry = initCacheVersionMapEntryFactory(cacheVersionMap);

    const baseFetch = options.fetch ?? fetch;
    const baseInit: RequestInit = { ...options.init };

    function fillClientFetcher(
        schema: Schema,
        names: readonly string[],
        result: any,
    ) {
        dir({ url, names });
    
        for (const [name, schemaEntry] of Object.entries(schema) as [string, any][]) {
            const kebabName = kebabTransformer.transform(name);
    
            if (isEndpoint(schemaEntry)) {
                const path = ["", ...names].join("/");
                const method = name as Method;
    
                initCacheVersionMapEntry(path);

                function parseArgs(args: any[]): [unknown, ClientFetcherInit | undefined] {
                    if (schemaEntry.input === null) {
                        return [undefined, { ...args[0] }];
                    }

                    return [args[0], args[1]];
                }
    
                const obj = {
                    [method]: async function (...args: any[]) {
                        const [input, init] = parseArgs(args);

                        if (schemaEntry.input != null && !input) {
                            throw new Error("Input data argument not provided.")
                        }

                        log(`Performing ${method.toUpperCase()} ${path}...`);
                        dir({ input, init });
    
                        const currentUrl = new URL(path, url);
                        const cacheVersion = cacheVersionMap.get(path)!;
                        dir({ initialUrl: currentUrl, cacheVersion });
    
                        currentUrl.searchParams.set("v", cacheVersion.toString());
    
                        if (schemaEntry.input !== null && input !== undefined) {
                            const serializedInput = transformer.serialize(input);
                            dir({ serializedInput });
    
                            currentUrl.searchParams.set(
                                "__body",
                                encodeURIComponent(serializedInput),
                            );
                        }
    
                        dir({ finalUrl: currentUrl });

                        const fetch = init?.fetch ?? baseFetch;
                        delete init?.fetch;
    
                        const fetchResult = await fetch(
                            currentUrl.origin + currentUrl.pathname + currentUrl.search,
                            {
                                ...baseInit,
                                ...init,

                                headers: Object.assign(
                                    {
                                        ...baseInit.headers,
                                        ...init?.headers,
                                    },
                                    schemaEntry.cacheControl
                                        ? { "Cache-Control": schemaEntry.cacheControl }
                                        : null,
                                ),

                                method,
                            },
                        );

                        await options.interceptor?.({
                            method,
                            path,
                            response: fetchResult,
                        });
    
                        if (fetchResult.ok) {
                            if (schemaEntry.output === null) {
                                return null;
                            }
    
                            const rawOutput = await fetchResult.text();
                            const preparedOutput = transformer.deserialize(rawOutput);
    
                            dir({
                                rawOutput,
                                preparedOutput,
                            });

                            dir({
                                enableAutoScopeInvalidation: schemaEntry.enableAutoScopeInvalidation,
                                invalidate: schemaEntry.invalidate,
                            });

                            dir(
                                "before invalidations",
                                {
                                    cacheVersionMap,
                                }
                            )
    
                            if (schemaEntry.enableAutoScopeInvalidation ?? true) {
                                updateLocalStorageCacheVersion(
                                    cacheVersionMap,
                                    path,
                                );
                            }
    
                            if (schemaEntry.invalidate) {
                                for (const invalidate of schemaEntry.invalidate) {
                                    updateLocalStorageCacheVersion(
                                        cacheVersionMap,
                                        invalidate,
                                    );
                                }
                            }

                            dir(
                                "after invalidations",
                                {
                                    cacheVersionMap,
                                }
                            )
    
                            return preparedOutput;
                        }
                        
                        throw new HttpError(
                            fetchResult.status,
                            await fetchResult.text() || fetchResult.statusText,
                        )
                    },
                };
    
                Object.assign(result, obj);
            }
            else {
                const nestedResult = result[name] = {};
    
                fillClientFetcher(
                    schemaEntry,
                    [...names, kebabName],
                    nestedResult,
                );
            }
        }
    
        return result;
    }

    const fetcher: ClientFetcher<TSchema> = fillClientFetcher(
        schema,
        [],
        {},
    );

    return {
        fetcher,
    };
}
