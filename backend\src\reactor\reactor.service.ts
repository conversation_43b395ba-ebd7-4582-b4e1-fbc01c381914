import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    Logger,
    NotFoundException,
} from "@nestjs/common";
import { Prisma, ReactorRatingType } from "@prisma/client";
import {
    toPrismaLocalizations,
    toPrismaLocalizationsWhere,
    toPrismaPagination,
} from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { CurrentUser } from "src/auth/types";
import { getError } from "src/common/errors";
import { ReactorLensService } from "./lens/lens.service";
import { Common, Reactor } from "@commune/api";
import { RatingService } from "src/rating/rating.service";
import { MinioService } from "src/minio/minio.service";
import { Sql } from "@prisma/client/runtime/library";
import { faker, fakerEN, fakerRU } from "@faker-js/faker";

export type Post = {
    id: string;

    author: {
        id: string;
        name: Common.Localization[];
        avatar: string | null;
    };

    rating: {
        likes: number;
        dislikes: number;
        status: ReactorRatingType | null;
    };

    usefulness: {
        value: number | null;
        count: number;
        totalValue: number;
    };

    title: Common.Localization[];
    body: Common.Localization[];

    tags: string[];

    createdAt: Date;
    updatedAt: Date;
};

export type PostsResponse = {
    items: Post[];
    total: number;
};

type RawComment = {
    id: string;

    post_id: string;
    path: string;
    internal_number: number;

    author_id: string;
    is_anonymous: boolean;
    anonimity_reason: string | null;

    rating_likes: number;
    rating_dislikes: number;
    rating_status: ReactorRatingType | null;

    children_count: number;

    delete_reason: string | null;

    created_at: Date;
    updated_at: Date;
    deleted_at: Date | null;
};

export type Comment = {
    id: string;

    path: string;
    internalNumber: number;

    author: {
        id: string;
        name: Common.Localization[];
        avatar: string | null;
    } | null;
    isAnonymous: boolean;
    anonimityReason: string | null;

    rating: {
        likes: number;
        dislikes: number;
        status: ReactorRatingType | null;
    };

    body: Common.Localization[] | null;

    childrenCount: number;

    deleteReason: string | null;

    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
};

export type CommentsResponse = {
    items: Comment[];
    total: number;
};

const ID_0 = "0".repeat(21);
const ID_1 = "0".repeat(20) + "1";
const ID_2 = "0".repeat(20) + "2";

function mockId(index: number) {
    return "0".repeat(20) + index.toString();
}

function range(from: number, to: number) {
    return new Array(to - from + 1).fill(0).map((_, i) => from + i);
}

function randomRange(min: number, max: number) {
    return range(min, faker.number.int({ min, max }));
}

@Injectable()
export class ReactorService {
    private readonly logger = new Logger(ReactorService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
        private readonly lensService: ReactorLensService,
        private readonly ratingService: RatingService,
    ) {}

    async getPosts(
        pagination: { page: number; size: number } | undefined,
        user: CurrentUser,
    ): Promise<PostsResponse> {
        const { skip, take } = toPrismaPagination(pagination);

        // Get total count of posts
        const total = await this.prisma.reactorPost.count({
            where: {
                deletedAt: null,
            },
        });

        // Get paginated posts with author information and images
        const posts = await this.prisma.reactorPost.findMany({
            where: {
                deletedAt: null,
            },
            include: {
                author: {
                    include: {
                        name: true,
                        images: {
                            orderBy: {
                                createdAt: "desc",
                            },
                            take: 1,
                        },
                    },
                },

                title: true,
                body: true,

                tags: true,
            },
            orderBy: {
                createdAt: "desc",
            },
            skip,
            take,
        });

        // Get rating aggregations for all posts
        const ratingAggregations = await this.prisma.reactorRating.groupBy({
            by: ["entityId", "type"],
            where: {
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
            _count: {
                type: true,
            },
        });

        // Get current user's ratings
        const userRatings = await this.prisma.reactorRating.findMany({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
        });

        // Get usefulness aggregations for all posts
        const usefulnessAggregations =
            await this.prisma.reactorUsefulness.groupBy({
                by: ["entityId"],
                where: {
                    entityType: "post",
                    entityId: {
                        in: posts.map((post) => post.id),
                    },
                },
                _sum: {
                    value: true,
                },
                _count: {
                    value: true,
                },
            });

        // Get current user's usefulness ratings
        const userUsefulness = await this.prisma.reactorUsefulness.findMany({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: {
                    in: posts.map((post) => post.id),
                },
            },
        });

        // Transform the data into the required format
        const transformedPosts = posts.map((post) => {
            // Calculate rating aggregations for this post
            const postRatings = ratingAggregations.filter(
                (r) => r.entityId === post.id,
            );
            const likes =
                postRatings.find((r) => r.type === "like")?._count.type || 0;
            const dislikes =
                postRatings.find((r) => r.type === "dislike")?._count.type || 0;
            const userRating = userRatings.find((r) => r.entityId === post.id);

            // Calculate usefulness aggregations for this post
            const postUsefulness = usefulnessAggregations.find(
                (u) => u.entityId === post.id,
            );
            const userUsefulnessRating = userUsefulness.find(
                (u) => u.entityId === post.id,
            );

            return {
                id: post.id,

                author: {
                    id: post.author.id,
                    name: post.author.name,
                    avatar: post.author.images[0]?.url ?? null,
                },

                rating: {
                    likes,
                    dislikes,
                    status: userRating?.type ?? null,
                },

                usefulness: {
                    value: userUsefulnessRating?.value ?? null,
                    totalValue: postUsefulness?._sum.value ?? 0,
                    count: postUsefulness?._count.value ?? 0,
                },

                title: post.title,
                body: post.body,

                tags: post.tags.map((t) => t.id),

                createdAt: post.createdAt,
                updatedAt: post.updatedAt,
            };
        });

        return {
            items: transformedPosts,
            total,
        };
    }

    async getPost(id: string, user: CurrentUser) {
        const post = await this.prisma.reactorPost.findUnique({
            where: {
                id,
                deletedAt: null,
            },
            include: {
                author: {
                    include: {
                        name: true,
                        images: {
                            orderBy: {
                                createdAt: "desc",
                            },
                            take: 1,
                        },
                    },
                },

                title: true,
                body: true,

                tags: true,
            },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        const [likes, dislikes, userRating, usefulness, userUsefulness] =
            await Promise.all([
                this.prisma.reactorRating.count({
                    where: {
                        entityType: "post",
                        entityId: post.id,
                        type: "like",
                    },
                }),
                this.prisma.reactorRating.count({
                    where: {
                        entityType: "post",
                        entityId: post.id,
                        type: "dislike",
                    },
                }),
                this.prisma.reactorRating.findUnique({
                    where: {
                        userId_entityType_entityId: {
                            entityId: post.id,
                            entityType: "post",
                            userId: user.id,
                        },
                    },
                }),

                this.prisma.reactorUsefulness.aggregate({
                    where: {
                        entityType: "post",
                        entityId: post.id,
                    },
                    _sum: {
                        value: true,
                    },
                    _count: {
                        value: true,
                    },
                }),
                this.prisma.reactorUsefulness.findUnique({
                    where: {
                        userId_entityType_entityId: {
                            entityId: post.id,
                            entityType: "post",
                            userId: user.id,
                        },
                    },
                }),
            ]);

        return {
            id: post.id,

            author: {
                id: post.author.id,
                name: post.author.name,
                avatar: post.author.images[0]?.url ?? null,
            },

            rating: {
                likes,
                dislikes,
                status: userRating?.type ?? null,
            },

            usefulness: {
                value: userUsefulness?.value ?? null,
                count: usefulness._count.value ?? 0,
                totalValue: usefulness._sum.value ?? 0,
            },

            title: post.title,
            body: post.body,

            tags: post.tags.map((t) => t.id),

            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
        };
    }

    async createPost(
        dto: {
            title: Common.Localization[];
            body: Common.Localization[];
            tags: string[];
        },
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.create({
            data: {
                authorId: user.id,

                title: {
                    create: dto.title.map((t) => ({
                        key: "title",
                        value: t.value,
                        locale: t.locale,
                    })),
                },

                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },

                tags: {
                    connect: dto.tags.map((tag) => ({ id: tag })),
                },
            },
        });

        return { id: post.id };
    }

    async updatePost(
        id: string,
        dto: Partial<{
            title: Common.Localization[];
            body: Common.Localization[];
            tags: string[];
        }>,
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        if (user.role !== "admin" && user.id !== post.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorPost.update({
                where: { id },
                data: {
                    title: dto.title && {
                        deleteMany: {},
                    },
                    body: dto.body && {
                        deleteMany: {},
                    },
                },
            });

            await trx.reactorPost.update({
                where: { id },
                data: {
                    title: dto.title && {
                        create: dto.title.map((t) => ({
                            key: "title",
                            value: t.value,
                            locale: t.locale,
                        })),
                    },
                    body: dto.body && {
                        create: dto.body.map((b) => ({
                            key: "body",
                            value: b.value,
                            locale: b.locale,
                        })),
                    },
                    tags: dto.tags && {
                        set: dto.tags.map((tag) => ({ id: tag })),
                    },
                },
            });
        });

        return true;
    }

    async updatePostRating(
        id: string,
        dto: {
            type: ReactorRatingType;
        },
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: post.id,
            },
        });

        let newStatus: ReactorRatingType | null = null;

        if (existingRating) {
            if (existingRating.type === dto.type) {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.delete({
                        where: { id: existingRating.id },
                    });

                    await this.ratingService.deleteRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: post.authorId,
                            entityType: "post",
                            entityId: post.id,
                        },
                        trx,
                    );
                });
            } else {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.update({
                        where: { id: existingRating.id },
                        data: {
                            type: dto.type,
                        },
                    });

                    await this.ratingService.upsertRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: post.authorId,
                            entityType: "post",
                            entityId: post.id,
                            value: dto.type === "like" ? 1 : -1,
                        },
                        trx,
                    );
                });

                newStatus = dto.type;
            }
        } else {
            await this.prisma.$transaction(async (trx) => {
                await trx.reactorRating.create({
                    data: {
                        userId: user.id,
                        entityType: "post",
                        entityId: post.id,
                        type: dto.type,
                    },
                });

                await this.ratingService.upsertRelativeUserRating(
                    {
                        sourceUserId: user.id,
                        targetUserId: post.authorId,
                        entityType: "post",
                        entityId: post.id,
                        value: dto.type === "like" ? 1 : -1,
                    },
                    trx,
                );
            });

            newStatus = dto.type;
        }

        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "dislike",
                },
            }),
        ]);

        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }

    async updatePostUsefulness(
        id: string,
        dto: {
            value: number | null;
        },
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        if (dto.value === null) {
            await this.prisma.reactorUsefulness.delete({
                where: {
                    userId_entityType_entityId: {
                        entityId: post.id,
                        entityType: "post",
                        userId: user.id,
                    },
                },
            });
        } else {
            await this.prisma.reactorUsefulness.upsert({
                where: {
                    userId_entityType_entityId: {
                        entityId: post.id,
                        entityType: "post",
                        userId: user.id,
                    },
                },
                create: {
                    entityId: post.id,
                    entityType: "post",
                    userId: user.id,
                    value: dto.value,
                },
                update: {
                    value: dto.value,
                },
            });
        }

        const [count, totalValue] = await Promise.all([
            this.prisma.reactorUsefulness.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
            }),
            this.prisma.reactorUsefulness.aggregate({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
                _avg: {
                    value: true,
                },
            }),
        ]);

        return {
            count,
            totalValue: totalValue._avg.value,
            value: dto.value,
        };
    }

    async deletePost(
        id: string,
        dto: { reason: string | null },
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        if (user.role !== "admin" && user.id !== post.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorPost.update({
            where: { id },
            data: {
                deleteReason: dto.reason,
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async createMockPost() {
        await this.prisma.user.createMany({
            data: range(1, 20).map((_, i) => ({
                id: mockId(i),
                email: fakerEN.internet.email(),
                role: "user",
            })),
            skipDuplicates: true,
        });

        const tags = await Promise.all(
            randomRange(2, 5).map(() =>
                this.prisma.tag.create({
                    data: {
                        name: {
                            create: toPrismaLocalizations(
                                [
                                    {
                                        locale: "en",
                                        value: fakerEN.word.sample(),
                                    },
                                    {
                                        locale: "ru",
                                        value: fakerRU.word.sample(),
                                    },
                                ],
                                "name",
                            ),
                        },
                    },
                }),
            ),
        );

        const hub = await this.prisma.reactorHub.create({
            data: {
                headUserId: ID_0,
                name: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.word.words(),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.word.words(),
                            },
                        ],
                        "name",
                    ),
                },
                description: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.lorem.words(50),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.lorem.words(50),
                            },
                        ],
                        "description",
                    ),
                },
            },
        });

        const community = await this.prisma.reactorCommunity.create({
            data: {
                headUserId: ID_0,
                hubId: hub.id,
                name: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.word.words(),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.word.words(),
                            },
                        ],
                        "name",
                    ),
                },
                description: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.lorem.words(50),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.lorem.words(50),
                            },
                        ],
                        "description",
                    ),
                },
            },
        });

        const post = await this.prisma.reactorPost.create({
            data: {
                authorId: ID_1,
                hubId: hub.id,
                communityId: community.id,
                title: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.word.words(),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.word.words(),
                            },
                        ],
                        "title",
                    ),
                },
                body: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.lorem.words(50),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.lorem.words(50),
                            },
                        ],
                        "body",
                    ),
                },
                tags: {
                    connect: tags,
                },
            },
        });

        await this.prisma.reactorRating.createMany({
            data: randomRange(1, 10).map((_, i) => ({
                userId: mockId(i),
                entityType: "post",
                entityId: post.id,
                type: "like",
            })),
        });

        await this.prisma.reactorRating.createMany({
            data: randomRange(1, 10).map((_, i) => ({
                userId: mockId(10 + i),
                entityType: "post",
                entityId: post.id,
                type: "dislike",
            })),
        });

        await this.prisma.reactorUsefulness.createMany({
            data: randomRange(1, 10).map((_, i) => ({
                userId: mockId(i),
                entityType: "post",
                entityId: post.id,
                value: faker.number.int({ min: 1, max: 10 }),
            })),
        });

        return post;
    }

    async getPosts2(
        data: {
            id?: string;
            lensId?: string | null;
        },
        pagination: Common.Pagination,
        user: CurrentUser,
    ) {
        let lensSql: string | null = null;

        if (data.lensId) {
            const lens = await this.prisma.reactorLens.findUnique({
                where: { id: data.lensId },
            });

            if (!lens) {
                throw new NotFoundException(...getError("lens_not_found"));
            }

            lensSql = lens.sql;
        }

        if (true) {
            // lensSql = "hub_id IN ('oTgqd4iLcWKXLlUkOlRsU')";
            // lensSql = "(hub_id NOT IN ('oTgqd4iLcWKXLlUkOlRsU', 'wNKuCEJO5g3ZfV0oWk51L') AND community_id NOT IN ('3-9iEDWNMAQSyZFPzZiQe'))";
            // lensSql = "EXISTS (SELECT 1 FROM UNNEST(body_values) AS b WHERE b ILIKE '%рес%')";
            // lensSql = "age < 16500";
        }

        const wheres = [];

        if (data.id) {
            wheres.push(`(posts.id = '${data.id}')`);
        }

        if (!user.isAdmin) {
            wheres.push("(posts.deleted_at IS NULL)");
        }

        const posts = await this.prisma.$queryRaw<Reactor.GetPosts2Response>`
            SELECT
                id,

                hub,
                community,

                author,

                rating,
                usefulness,

                title,
                body,

                tags,

                created_at AS "createdAt",
                updated_at AS "updatedAt"

                ${user.isAdmin ? new Sql([`, deleted_at AS "deletedAt"`], []) : new Sql([""], [])}
            FROM (
                SELECT
                    posts.id,

                    posts.hub_id,
                    CASE WHEN hub_data.id IS NOT NULL THEN to_jsonb(hub_data) END AS hub,

                    posts.community_id,
                    CASE WHEN community_data.id IS NOT NULL THEN to_jsonb(community_data) END AS community,

                    posts.author_id,
                    to_jsonb(author_data) AS author,

                    post_rating_data.likes - post_rating_data.dislikes AS total_rating,
                    JSONB_BUILD_OBJECT(
                        'likes', post_rating_data.likes,
                        'dislikes', post_rating_data.dislikes,
                        'status', post_rating_status_data.status
                    ) AS rating,

                    post_usefulness_total_data.value AS average_usefulness,
                    JSONB_BUILD_OBJECT(
                        'value', post_usefulness_status_data.value,
                        'count', post_usefulness_total_data.count,
                        'totalValue', post_usefulness_total_data.value
                    ) AS usefulness,

                    post_title_data.title,
                    post_body_data.body,

                    tag_data.tag_ids,
                    tag_data.tags,

                    EXTRACT(EPOCH FROM (NOW() - posts.created_at))::INT AS age,

                    posts.created_at,
                    posts.updated_at,
                    posts.deleted_at
                FROM reactor_posts posts

                LEFT JOIN LATERAL (
                    SELECT
                        posts.hub_id AS id,
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', l.locale,
                                'value',  l.value
                            )
                        ) AS name,
                        MAX(images.url) AS image
                    FROM _reactor_hub_name
                    LEFT JOIN localizations l ON l.id = _reactor_hub_name."A"
                    LEFT JOIN images ON images.id = _reactor_hub_name."B"
                    WHERE _reactor_hub_name."B" = posts.hub_id
                ) AS hub_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        posts.community_id AS id,
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', l.locale,
                                'value',  l.value
                            )
                        ) AS name,
                        MAX(images.url) AS image
                    FROM _reactor_community_name
                    LEFT JOIN localizations l ON l.id = _reactor_community_name."A"
                    LEFT JOIN images ON images.id = _reactor_community_name."B"
                    WHERE _reactor_community_name."B" = posts.community_id
                ) AS community_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        posts.author_id AS id,
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', l.locale,
                                'value',  l.value
                            )
                        ) AS name,
                        MAX(images.url) AS image
                    FROM _user_name
                    LEFT JOIN localizations l ON l.id = _user_name."A"
                    LEFT JOIN _user_images ON _user_images."B" = _user_name."B"
                    LEFT JOIN LATERAL (
                        SELECT id, url
                        FROM images
                        WHERE images.id = _user_images."A"
                        ORDER BY created_at DESC
                        LIMIT 1
                    ) AS images ON true
                    WHERE _user_name."B" = posts.author_id
                ) AS author_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        COALESCE(ARRAY_AGG(_reactor_post_tags."B"), '{}') AS tag_ids,
                        COALESCE(JSONB_AGG(to_jsonb(tag_names_data)), '[]') AS tags
                    FROM _reactor_post_tags
                    LEFT JOIN LATERAL (
                        SELECT
                            _reactor_post_tags."B" AS id,
                            JSONB_AGG(
                                JSONB_BUILD_OBJECT(
                                    'locale', l.locale,
                                    'value',  l.value
                                )
                            ) AS name
                        FROM _tag_name
                        LEFT JOIN localizations l ON l.id = _tag_name."A"
                        WHERE _tag_name."B" = _reactor_post_tags."B"
                        GROUP BY _reactor_post_tags."B"
                    ) AS tag_names_data ON true
                    WHERE _reactor_post_tags."A" = posts.id
                ) AS tag_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', post_title.locale,
                                'value', post_title.value
                            )
                        ) AS title
                    FROM _reactor_post_title
                    LEFT JOIN localizations post_title
                        ON post_title.id = _reactor_post_title."A"
                    WHERE _reactor_post_title."B" = posts.id
                ) AS post_title_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', post_body.locale,
                                'value', post_body.value
                            )
                        ) AS body
                    FROM _reactor_post_body
                    LEFT JOIN localizations post_body
                        ON post_body.id = _reactor_post_body."A"
                    WHERE _reactor_post_body."B" = posts.id
                ) AS post_body_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        COUNT(CASE WHEN rating.type = 'like' THEN 1 END)::INT AS likes,
                        COUNT(CASE WHEN rating.type = 'dislike' THEN 1 END)::INT AS dislikes
                    FROM reactor_ratings rating
                    WHERE
                        rating.entity_id = posts.id
                        AND rating.entity_type = 'post'

                ) AS post_rating_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        rating.type AS status
                    FROM reactor_ratings rating
                    WHERE
                        rating.entity_id = posts.id
                        AND rating.entity_type = 'post'
                        AND rating.user_id = ${user.id}
                ) AS post_rating_status_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        usefulness.value AS value
                    FROM reactor_usefulnesses usefulness
                    WHERE
                        usefulness.entity_id = posts.id
                        AND usefulness.entity_type = 'post'
                        AND usefulness.user_id = ${user.id}
                ) AS post_usefulness_status_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        AVG(usefulness.value)::FLOAT AS value,
                        COUNT(usefulness.id)::INT AS count
                    FROM reactor_usefulnesses usefulness
                    WHERE
                        usefulness.entity_id = posts.id
                        AND usefulness.entity_type = 'post'
                ) AS post_usefulness_total_data ON true

                ${
                    wheres.length
                        ? new Sql([`WHERE ${wheres.join(" AND ")}`], [])
                        : new Sql([""], [])
                }
            ) posts

            ${lensSql ? new Sql([`WHERE ${lensSql}`], []) : new Sql([""], [])}

            ORDER BY created_at DESC

            LIMIT ${pagination.size}
            OFFSET ${(pagination.page - 1) * pagination.size}
        `;

        return posts;
    }

    async getPost2(id: string, user: CurrentUser) {
        const [post] = await this.getPosts2({ id }, { page: 1, size: 1 }, user);

        return post;
    }

    async getComments(
        dto: {
            entityType: "post" | "comment";
            entityId: string;
        },
        user: CurrentUser,
    ): Promise<CommentsResponse> {
        if (dto.entityType === "comment") {
            throw new BadRequestException(
                ...getError("get_comments_for_comment_not_implemented"),
            );
        }

        const { entityId } = dto;

        const post = await this.prisma.reactorPost.findUnique({
            where: { id: entityId },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        const rawComments = await this.prisma.$queryRaw<RawComment[]>`
            SELECT
                comments.id,

                comments.post_id,

                comments.path,
                comments.internal_number,

                COUNT(rating_likes.id)::INT AS rating_likes,
                COUNT(rating_dislikes.id)::INT AS rating_dislikes,
                rating_status.type AS rating_status,

                comments.author_id,
                comments.is_anonymous,
                comments.anonimity_reason,

                COUNT(children.id)::INT - 1 AS children_count,

                comments.delete_reason,

                comments.created_at,
                comments.updated_at,
                comments.deleted_at

            FROM reactor_comments comments

            LEFT JOIN reactor_comments children
                ON children.path <@ comments.path
                AND children.post_id = comments.post_id

            LEFT JOIN reactor_ratings rating_likes
                ON rating_likes.entity_id = comments.id
                AND rating_likes.entity_type = 'comment'
                AND rating_likes.type = 'like'

            LEFT JOIN reactor_ratings rating_dislikes
                ON rating_dislikes.entity_id = comments.id
                AND rating_dislikes.entity_type = 'comment'
                AND rating_dislikes.type = 'dislike'

            LEFT JOIN reactor_ratings rating_status
                ON rating_status.entity_id = comments.id
                AND rating_status.entity_type = 'comment'
                AND rating_status.user_id = ${user.id}

            WHERE comments.post_id = ${entityId}

            GROUP BY
                comments.id,
                rating_status.type

            ORDER BY path ASC
        `;

        const users = await this.prisma.user.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.author_id),
                },
            },
            include: {
                name: true,
                images: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 1,
                },
            },
        });

        const userMap = new Map(users.map((u) => [u.id, u]));

        const commentBodies = await this.prisma.reactorComment.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.id),
                },
            },
            include: {
                body: true,
            },
        });

        const commentBodyMap = new Map(
            commentBodies.map((c) => [c.id, c.body]),
        );

        const comments = rawComments.map<Comment>((c) => {
            const user = userMap.get(c.author_id)!;
            const body = commentBodyMap.get(c.id)!;

            return {
                id: c.id,

                path: c.path,
                internalNumber: c.internal_number,

                author: c.is_anonymous
                    ? null
                    : {
                          id: user.id,
                          name: user.name,
                          avatar: user.images[0]?.url ?? null,
                      },
                isAnonymous: c.is_anonymous,
                anonimityReason: c.anonimity_reason,

                rating: {
                    likes: c.rating_likes,
                    dislikes: c.rating_dislikes,
                    status: c.rating_status,
                },

                body: c.deleted_at ? null : body,

                childrenCount: c.children_count,

                deleteReason: c.delete_reason,

                createdAt: c.created_at,
                updatedAt: c.updated_at,
                deletedAt: c.deleted_at,
            };
        });

        return {
            items: comments,
            total: 0,
        };
    }

    async getComment(dto: { id: string }, user: CurrentUser): Promise<Comment> {
        const { id } = dto;

        const rawComment = await this.prisma.$queryRaw<RawComment[]>`
            SELECT
                comments.id,

                comments.post_id,

                comments.path,
                comments.internal_number,

                COUNT(DISTINCT rating_likes.id)::INT AS rating_likes,
                COUNT(DISTINCT rating_dislikes.id)::INT AS rating_dislikes,
                rating_status.type AS rating_status,

                comments.author_id,
                comments.is_anonymous,
                comments.anonimity_reason,

                COUNT(DISTINCT children.id)::INT - 1 AS children_count,

                comments.delete_reason,

                comments.created_at,
                comments.updated_at,
                comments.deleted_at

            FROM reactor_comments comments

            LEFT JOIN reactor_comments children
                ON children.path <@ comments.path
                AND children.post_id = comments.post_id

            LEFT JOIN reactor_ratings rating_likes
                ON rating_likes.entity_id = comments.id
                AND rating_likes.entity_type = 'comment'
                AND rating_likes.type = 'like'

            LEFT JOIN reactor_ratings rating_dislikes
                ON rating_dislikes.entity_id = comments.id
                AND rating_dislikes.entity_type = 'comment'
                AND rating_dislikes.type = 'dislike'

            LEFT JOIN reactor_ratings rating_status
                ON rating_status.entity_id = comments.id
                AND rating_status.entity_type = 'comment'
                AND rating_status.user_id = ${user.id}

            WHERE comments.id = ${id}

            GROUP BY
                comments.id,
                rating_status.type

            ORDER BY path ASC

            LIMIT 1
        `.then((r) => r[0]);

        if (!rawComment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        const author = await this.prisma.user.findUnique({
            where: { id: rawComment.author_id },
            include: {
                name: true,
                images: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 1,
                },
            },
        });

        if (!author) {
            throw new NotFoundException(...getError("user_not_found"));
        }

        const body = await this.prisma.reactorComment
            .findUnique({
                where: {
                    id: rawComment.id,
                },
                select: {
                    body: true,
                },
            })
            .then((r) => r!.body);

        return {
            id: rawComment.id,

            path: rawComment.path,
            internalNumber: rawComment.internal_number,

            author: rawComment.is_anonymous
                ? null
                : {
                      id: author.id,
                      name: author.name,
                      avatar: author.images[0]?.url ?? null,
                  },
            isAnonymous: rawComment.is_anonymous,
            anonimityReason: rawComment.anonimity_reason,

            rating: {
                likes: rawComment.rating_likes,
                dislikes: rawComment.rating_dislikes,
                status: rawComment.rating_status,
            },

            body: rawComment.deleted_at ? null : body,

            childrenCount: rawComment.children_count,

            deleteReason: rawComment.delete_reason,

            createdAt: rawComment.created_at,
            updatedAt: rawComment.updated_at,
            deletedAt: rawComment.deleted_at,
        };
    }

    private async getNextCommentInternalNumber(postId: string) {
        const { internalNumber } =
            await this.prisma.reactorPostInternalNumber.upsert({
                where: { postId },
                update: { internalNumber: { increment: 1 } },
                create: { postId, internalNumber: 1 },
            });

        return internalNumber;
    }

    async createComment(
        dto: {
            entityType: "post" | "comment";
            entityId: string;
            body: Common.Localization[];
        },
        user: CurrentUser,
    ): Promise<{ id: string }> {
        const { entityType, entityId, body } = dto;

        if (entityType === "post") {
            const comment = await this.createPostComment(
                {
                    postId: entityId,
                    body,
                },
                user,
            );

            return { id: comment.id };
        }

        if (entityType === "comment") {
            const comment = await this.createCommentComment(
                {
                    commentId: entityId,
                    body,
                },
                user,
            );

            return { id: comment.id };
        }

        throw new Error("Impossible");
    }

    private async createPostComment(
        dto: {
            postId: string;
            body: Common.Localization[];
        },
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: dto.postId },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        const internalNumber = await this.getNextCommentInternalNumber(post.id);

        return await this.prisma.reactorComment.create({
            data: {
                postId: post.id,
                internalNumber,
                path: internalNumber.toString(),
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }

    private async createCommentComment(
        dto: {
            commentId: string;
            body: Common.Localization[];
        },
        user: CurrentUser,
    ) {
        const parentComment = await this.prisma.reactorComment.findUnique({
            where: { id: dto.commentId },
        });

        if (!parentComment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        const internalNumber = await this.getNextCommentInternalNumber(
            parentComment.postId,
        );

        return await this.prisma.reactorComment.create({
            data: {
                postId: parentComment.postId,
                internalNumber,
                path: `${parentComment.path}.${internalNumber}`,
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }

    async updateComment(
        id: string,
        dto: Partial<{
            body: Common.Localization[];
        }>,
        user: CurrentUser,
    ) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorComment.update({
                where: { id },
                data: {
                    body: dto.body && {
                        deleteMany: {},
                    },
                },
            });

            await trx.reactorComment.update({
                where: { id },
                data: {
                    body: dto.body && {
                        create: dto.body.map((b) => ({
                            key: "body",
                            value: b.value,
                            locale: b.locale,
                        })),
                    },
                },
            });
        });

        return true;
    }

    async updateCommentRating(
        id: string,
        dto: { type: ReactorRatingType },
        user: CurrentUser,
    ) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "comment",
                entityId: comment.id,
            },
        });

        let newStatus: ReactorRatingType | null = null;

        if (existingRating) {
            if (existingRating.type === dto.type) {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.delete({
                        where: { id: existingRating.id },
                    });

                    await this.ratingService.deleteRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: comment.authorId,
                            entityType: "comment",
                            entityId: comment.id,
                        },
                        trx,
                    );
                });
            } else {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.update({
                        where: { id: existingRating.id },
                        data: {
                            type: dto.type,
                        },
                    });

                    await this.ratingService.upsertRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: comment.authorId,
                            entityType: "comment",
                            entityId: comment.id,
                            value: dto.type === "like" ? 1 : -1,
                        },
                        trx,
                    );
                });

                newStatus = dto.type;
            }
        } else {
            await this.prisma.$transaction(async (trx) => {
                await trx.reactorRating.create({
                    data: {
                        userId: user.id,
                        entityId: comment.id,
                        entityType: "comment",
                        type: dto.type,
                    },
                });

                await this.ratingService.upsertRelativeUserRating(
                    {
                        sourceUserId: user.id,
                        targetUserId: comment.authorId,
                        entityType: "comment",
                        entityId: comment.id,
                        value: dto.type === "like" ? 1 : -1,
                    },
                    trx,
                );
            });

            newStatus = dto.type;
        }

        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "dislike",
                },
            }),
        ]);

        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }

    async anonimifyComment(
        id: string,
        dto: { reason: string | null },
        user: CurrentUser,
    ) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorComment.update({
            where: { id },
            data: {
                isAnonymous: true,
                anonimityReason: dto.reason,
            },
        });

        return true;
    }

    async deleteComment(
        id: string,
        dto: { reason: string | null },
        user: CurrentUser,
    ) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorComment.update({
            where: { id },
            data: {
                deleteReason: dto.reason,
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async getLenses(user: CurrentUser) {
        return this.prisma.reactorLens.findMany({
            where: {
                userId: user.id,
                deletedAt: null,
            },
        });
    }

    private generateLensSql(code: string) {
        try {
            return this.lensService.generateSql(code);
        } catch (error) {
            this.logger.error(error);

            throw new BadRequestException(String(error));
        }
    }

    async createLens(dto: { name: string; code: string }, user: CurrentUser) {
        const sql = this.generateLensSql(dto.code);

        const lens = await this.prisma.reactorLens.create({
            data: {
                userId: user.id,
                name: dto.name,
                code: dto.code,
                sql,
            },
        });

        return { id: lens.id };
    }

    async updateLens(
        id: string,
        dto: { name?: string; code?: string },
        user: CurrentUser,
    ) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        const data: Prisma.ReactorLensUpdateInput = Object.assign(
            {},
            dto.name ? { name: dto.name } : undefined,
            dto.code
                ? { code: dto.code, sql: this.generateLensSql(dto.code) }
                : null,
        );

        await this.prisma.reactorLens.update({
            where: { id },
            data,
        });

        return true;
    }

    async deleteLens(id: string, user: CurrentUser) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorLens.update({
            where: { id },
            data: {
                deletedAt: new Date(),
            },
        });
    }

    async getHubs(
        data: {
            input: Reactor.GetHubsInput;
            pagination: Common.Pagination;
        },
        user: CurrentUser,
    ) {
        const { ids, query } = data.input;

        const hubs = await this.prisma.reactorHub.findMany({
            ...toPrismaPagination(data.pagination),
            where: Object.assign(
                {},
                ids && { id: { in: ids } },
                !user.isAdmin && { deletedAt: null },

                query && {
                    OR: [
                        {
                            name: toPrismaLocalizationsWhere(query),
                        },
                        {
                            description: toPrismaLocalizationsWhere(query),
                        },
                    ],
                },
            ),
            select: {
                id: true,
                headUser: {
                    include: {
                        name: true,
                        images: {
                            take: 1,
                        },
                    },
                },
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user.isAdmin,
            },
        });

        return hubs;
    }

    async createHub(input: Reactor.CreateHubInput, user: CurrentUser) {
        if (!user.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        const hub = await this.prisma.reactorHub.create({
            data: {
                headUserId: input.headUserId,
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: {
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });

        return hub;
    }

    async updateHub(
        id: string,
        input: Reactor.UpdateHubInput,
        user: CurrentUser,
    ) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        const { name, description } = input;

        await this.prisma.reactorHub.update({
            where: { id },
            data: {
                name: name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(name, "name"),
                },
                description: description && {
                    deleteMany: {},
                    create: toPrismaLocalizations(description, "description"),
                },
            },
        });
    }

    async updateHubImage(
        id: string,
        file: Express.Multer.File,
        user: CurrentUser,
    ) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadReactorHubImage(
                file,
                id,
            );

            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });

            await trx.reactorHub.update({
                where: { id },
                data: {
                    imageId: image.id,
                },
            });
        });
    }

    async deleteHubImage(id: string, user: CurrentUser) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
            include: {
                image: true,
            },
        });

        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        const hubImage = hub.image;

        if (!hubImage) {
            return;
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorHub.update({
                where: { id },
                data: {
                    imageId: null,
                },
            });

            await trx.image.update({
                where: { id: hubImage.id },
                data: {
                    deletedAt: new Date(),
                },
            });

            // await this.minioService.deleteReactorHubImage(id, hubImage.url);
        });
    }

    async getCommunities(
        data: {
            input: Reactor.GetCommunitiesInput;
            pagination: Common.Pagination;
        },
        user: CurrentUser,
    ) {
        const { ids, query, hubId } = data.input;

        const communities = await this.prisma.reactorCommunity.findMany({
            ...toPrismaPagination(data.pagination),
            where: Object.assign(
                {},
                ids && { id: { in: ids } },
                !user.isAdmin && { deletedAt: null },

                hubId && { hubId },

                query && {
                    OR: [
                        {
                            name: toPrismaLocalizationsWhere(query),
                        },
                        {
                            description: toPrismaLocalizationsWhere(query),
                        },
                    ],
                },
            ),
            select: {
                id: true,
                hub: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                headUser: {
                    include: {
                        name: true,
                        images: {
                            take: 1,
                        },
                    },
                },
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user.isAdmin,
            },
        });

        return communities;
    }

    async createCommunity(
        input: Reactor.CreateCommunityInput,
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        const community = await this.prisma.reactorCommunity.create({
            data: {
                headUserId: user.id,
                hubId: input.hubId,
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: {
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });

        return community;
    }

    async updateCommunity(
        id: string,
        input: Reactor.UpdateCommunityInput,
        user: CurrentUser,
    ) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        const { name, description } = input;

        await this.prisma.reactorCommunity.update({
            where: { id },
            data: {
                name: name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(name, "name"),
                },
                description: description && {
                    deleteMany: {},
                    create: toPrismaLocalizations(description, "description"),
                },
            },
        });
    }

    async updateCommunityImage(
        id: string,
        file: Express.Multer.File,
        user: CurrentUser,
    ) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        await this.prisma.$transaction(async (trx) => {
            const imageUrl =
                await this.minioService.uploadReactorCommunityImage(file, id);

            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });

            await trx.reactorCommunity.update({
                where: { id },
                data: {
                    imageId: image.id,
                },
            });
        });
    }

    async deleteCommunityImage(id: string, user: CurrentUser) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
            include: {
                image: true,
            },
        });

        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        const communityImage = community.image;

        if (!communityImage) {
            return;
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorCommunity.update({
                where: { id },
                data: {
                    imageId: null,
                },
            });

            await trx.image.update({
                where: { id: communityImage.id },
                data: {
                    deletedAt: new Date(),
                },
            });

            // await this.minioService.deleteReactorCommunityImage(id, communityImage.url);
        });
    }
}
