import type { Server } from "http";
import type { <PERSON><PERSON>, Schema } from "./acrpc2";

import util from "util";
import { z } from "zod";
import express from "express";
import { faker } from "@faker-js/faker";
import { createClient, createServer, superjsonTransformer } from "./acrpc2";

function dir(...args: any[]) {
    console.log(...args.map(arg => util.inspect(arg, { depth: null, colors: true })));
}

export const schema = {
    complexUser: {
        list: {
            get: {
                input: z.object({
                    ids: z.array(z.string()),
                    emails: z.array(z.string().email()),
                }),

                output: z.object({
                    list: z.array(
                        z.object({
                            id: z.string(),
                            email: z.string().email(),
                        })
                    ),
                }),

                requireMetadata: true,
            },
        },

        randomUser: {
            get: {
                input: undefined,
                output: z.object({
                    id: z.string(),
                    email: z.string().email(),
                }),

                invalidate: [
                    "/complex-user/list",
                    "/complex-user/complex-data",
                ],
            },
        },

        complexData: {
            get: {
                input: null,
                output: z.object({
                    string: z.string(),
                    number: z.number(),
                    boolean: z.boolean(),
                    null: z.null(),
                    array: z.array(z.any()),
                    object: z.record(z.string(), z.any()),
    
                    undefined: z.undefined(),
                    bigint: z.bigint(),
                    date: z.date(),
                    regexp: z.instanceof(RegExp),
                    set: z.set(z.any()),
                    map: z.map(z.string(), z.any()),
                    error: z.instanceof(Error),
                    url: z.instanceof(URL),
                })
            },
        },
    },
} satisfies Schema;

const handlers: Handlers<typeof schema> = {
    complexUser: {
        list: {
            async get(input) {
                return {
                    list: [
                        {
                            id: input.ids[0] ?? faker.string.nanoid(),
                            email: faker.internet.email(),
                        },
                        {
                            id: faker.string.nanoid(),
                            email: input.emails[0] ?? faker.internet.email(),
                        },
                    ]
                }
            }
        },

        randomUser: {
            get(input) {
                return {
                    id: faker.string.nanoid(),
                    email: faker.internet.email(),
                }
            }
        },

        complexData: {
            get(input) {
                return {
                    number: 42,
                    string: "Hello, world!",
                    boolean: true,
                    null: null,
                    array: [1, 2, 3],
                    object: {
                        a: 1,
                        b: 2,
                        c: 3,
                    },
    
                    undefined: undefined,
                    bigint: 424242424242424242424242424242424242424242424242424242424242424242424242424242424242n,
                    date: new Date(),
                    regexp: /test/,
                    set: new Set([1, 1, 2, 2, 3, 3]),
                    map: new Map([["a", 1], ["b", 2], ["c", 3]]),
                    error: new Error("Test error"),
                    url: new URL("https://example.com"),
                };
            }
        }
    }
};

export const transformer = superjsonTransformer;

export const server = createServer(
    schema,
    handlers,
    {
        transformer,
    }
);
export const router = server.router;

let __server: Server | undefined = undefined;

export function runServer(port = 4000) {
    const app = express();

    app.use(router);
    
    __server = app.listen(port, () => {    
        console.log(`Server is running on port ${port}`);
    });
}

export function stopServer() {
    if (__server) {
        __server.close((err) => {
            if (err) {
                console.error("Error closing server", err);
            }
            else {
                console.log("Server closed");
            }
        });
    }
}

export const client = createClient(schema, {
    entrypointUrl: "http://localhost:4000",
    transformer,
    interceptor: async ({ method, path, response }) => {
        dir("interceptor", { method, path, response });
    }
});
export const fetcher = client.fetcher;

// fetcher.complexUser.list.get({ ids: [], emails: [] });
// fetcher.complexUser.randomUser.get();
// fetcher.complexUser.complexData.get();
