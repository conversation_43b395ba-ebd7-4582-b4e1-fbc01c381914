{"name": "commune", "version": "0.0.1", "private": true, "workspaces": ["libs/api", "backend", "frontend"], "scripts": {"frontend:dev": "npm run dev --workspace=frontend", "backend:dev": "npm run dev --workspace=backend", "dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "api:build": "npm run build --workspace=libs/api"}, "devDependencies": {"typescript": "^5.8.3"}, "dependencies": {"@ocelotjungle/case-converters": "^1.0.0", "superjson": "^2.2.2", "tsup": "^8.5.0", "zod": "^3.25.76"}}