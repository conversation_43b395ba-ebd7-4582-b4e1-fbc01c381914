// @ts-nocheck
import type { PageLoad } from "./$types";

import { fixResponseJsonDatesForArray, handleUnauthorized } from "$lib";
import type { Reactor } from "@commune/api";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const response = await fetch("/api/reactor/post?page=1&size=20");

  handleUnauthorized(response, url);

  if (!response.ok) {
    return {
      posts: [],
      isHasMorePosts: false,
    };
  }

  const data = await response.json() as Reactor.GetPosts2Response;

  // Convert date strings to Date objects
  const posts: typeof data = fixResponseJsonDatesForArray(data);

  return {
    posts,
    isHasMorePosts: data.length === 20, // If we got a full page, there might be more
  };
};
