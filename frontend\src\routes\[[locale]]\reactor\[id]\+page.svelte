<script lang="ts">
  import type { Common, Reactor } from "@commune/api";
  import type { CommentEntity } from "./types";

  import { fetchWithAuth, fixResponseJsonDates } from "$lib";
  import LocalizedTextarea from "../../(index)/components/localized-textarea.svelte";
  import RightMenu from "../right-menu.svelte";
  import { CommentTree } from "./comment-tree";
  import Comment from "./comment.svelte";
  import PostCard from "../post-card.svelte";

  const i18n = {
    en: {
      reactor: "Reactor",
      usefulnesss: "Usefulness",
      comments: "Comments",
      commentPlaceholder: "Write your comment...",
      submit: "Submit",
      time: {
        days(n: number) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n: number) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n: number) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n: number) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now",
      },
    },
    ru: {
      reactor: "Реактор",
      usefulnesss: "Полезность",
      comments: "Комментарии",
      commentPlaceholder: "Напишите ваш комментарий...",
      submit: "Отправить",
      time: {
        getPlural(n: number) {
          if (n === 1) return 0;
          if (n >= 2 && n <= 4) return 1;

          return 2;
        },

        days(n: number) {
          const word = ["день", "дня", "дней"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        hours(n: number) {
          const word = ["час", "часа", "часов"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        minutes(n: number) {
          const word = ["минуту", "минуты", "минут"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        seconds(n: number) {
          const word = ["секунду", "секунды", "секунд"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        rightNow: "только что",
      },
    },
  };

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // Initialize with SSR data
  let post = $state<Reactor.GetPost2Response>(data.post);
  let rating = $state<Reactor.GetPost2Response["rating"]>(data.post.rating);
  let usefulness = $state<Reactor.GetPost2Response["usefulness"]>(data.post.usefulness);

  // Get the ID from the post data
  const id = $derived(post.id);

  const ratingValue = $derived(rating ? rating.likes - rating.dislikes : 0);
  const usefulnessValue = $derived(usefulness ? (usefulness.totalValue ?? 0) : 0);

  // Hover state for usefulness stars
  let hoveredStarIndex = $state<number | null>(null);

  const authorName = $derived(getAppropriateLocalization(post.author.name));
  const title = $derived(getAppropriateLocalization(post.title));
  const body = $derived(getAppropriateLocalization(post.body));

  // Initialize comments with SSR data
  let comments = $state<CommentEntity[]>(data.comments);
  const commentTree = $derived.by(() => comments && new CommentTree(comments));

  let copied = $state(false);
  let commentText = $state<Common.Localizations>([]);

  // Format date for display
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }

  function copyLink(evt: MouseEvent) {
    if (!evt.ctrlKey && !evt.shiftKey && !evt.altKey && !evt.metaKey) {
      evt.preventDefault();

      const link = `${window.location.origin}${toLocaleHref(`/reactor/${id}`)}`;
      navigator.clipboard.writeText(link);

      copied = true;

      setTimeout(() => (copied = false), 2000);
    }
  }

  async function addComment(id: string) {
    const response = await fetchWithAuth(`/api/reactor/comment/${id}`);

    if (response.ok) {
      const comment = await response.json();

      comments = [
        ...comments,
        {
          ...fixResponseJsonDates(comment),

          isMustBeTop: true,
        },
      ];

      if (commentTree) {
        const parentPath = commentTree.getParentPath(comment.path);

        if (parentPath) {
          commentTree.incrementChildrenCount(parentPath);
        }
      }
    }
  }

  async function like() {
    const response = await fetchWithAuth(`/api/reactor/post/${id}/rating`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "like",
      }),
    });

    if (response.ok) {
      rating = (await response.json()) as {
        likes: number;
        dislikes: number;
        status: Reactor.RatingType | null;
      };
    }
  }

  async function dislike() {
    const response = await fetchWithAuth(`/api/reactor/post/${id}/rating`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "dislike",
      }),
    });

    if (response.ok) {
      rating = (await response.json()) as {
        likes: number;
        dislikes: number;
        status: Reactor.RatingType | null;
      };
    }
  }

  async function rateUsefulness(value: number) {
    const response = await fetchWithAuth(`/api/reactor/post/${id}/usefulness`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        value: value === usefulness?.value ? null : value,
      }),
    });

    if (response.ok) {
      usefulness = (await response.json()) as {
        value: number | null;
        count: number;
        totalValue: number | null;
      };
    }
  }

  async function submitComment() {
    const response = await fetchWithAuth(`/api/reactor/comment`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        entityType: "post",
        entityId: id,
        body: commentText,
      }),
    });

    if (response.ok) {
      const comment = (await response.json()) as { id: string };

      addComment(comment.id);

      commentText = [];
    }
  }
</script>

<svelte:head>
  <title>{title} — {t.reactor}</title>
</svelte:head>

<div class="row g-4 mt-3">
  <div class="col-3"></div>

  <!-- Main Content (2-9 columns) -->
  <div class="col-6">
    <div class="post-detail">
      <PostCard {locale} {post} {toLocaleHref} {getAppropriateLocalization} />

      <!-- Comments Section -->
      <div class="comments-section mt-4">
        <h4 class="mb-3">{t.comments} ({comments.length})</h4>

        <div class="comments-list">
          {#if commentTree}
            {#each commentTree.getRootComments() as comment (comment.id)}
              <Comment
                {comment}
                {locale}
                {routeLocale}
                expanded={false}
                {commentTree}
                {addComment}
                {getAppropriateLocalization}
              />
            {/each}
          {/if}

          <!-- {#each comments as comment (comment.id)}
            <Comment {comment} {locale} expanded={false} />
          {/each} -->
        </div>
      </div>

      <!-- Post Comment Form -->
      <div class="post-comment-form mt-4">
        <LocalizedTextarea
          {locale}
          id="post-comment"
          label=""
          placeholder={t.commentPlaceholder}
          rows={3}
          bind:value={commentText}
          languageSelectPosition="bottom"
        >
          <button class="btn btn-success btn-sm" onclick={submitComment}>
            <i class="bi bi-send me-1"></i>
            {t.submit}
          </button>
        </LocalizedTextarea>
      </div>
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <RightMenu {locale} {toLocaleHref} />
  </div>
</div>

<style>
  .comments-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
  }
</style>
