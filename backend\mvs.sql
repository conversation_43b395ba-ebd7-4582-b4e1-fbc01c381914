DROP MATERIALIZED VIEW IF EXISTS reactor_hub_short_mv;

CREATE MATERIALIZED VIEW IF NOT EXISTS reactor_hub_short_mv AS
SELECT
  e.id,
  JSONB_AGG(
    JSONB_BUILD_OBJECT(
      'locale', l.locale,
      'value',  l.value
    )
    ORDER BY l.locale
  ) AS name,
  images.url AS image
FROM reactor_hubs e
JOIN _reactor_hub_name n ON n."B" = e.id
LEFT JOIN localizations l ON l.id = n."A"
LEFT JOIN images ON images.id = e.image_id
GROUP BY e.id, images.id;

grant select on reactor_hub_short_mv to commune;

select * from reactor_hub_short_mv;





DROP MATERIALIZED VIEW IF EXISTS reactor_community_short_mv;

CREATE MATERIALIZED VIEW IF NOT EXISTS reactor_community_short_mv AS
SELECT
  e.id,
  JSONB_AGG(
    JSONB_BUILD_OBJECT(
      'locale', l.locale,
      'value',  l.value
    )
    ORDER BY l.locale
  ) AS name,
  images.url AS image
FROM reactor_communities e
JOIN _reactor_community_name n ON n."B" = e.id
LEFT JOIN localizations l ON l.id = n."A"
LEFT JOIN images ON images.id = e.image_id
GROUP BY e.id, images.id;

grant select on reactor_community_short_mv to commune;

select * from reactor_community_short_mv;










DROP MATERIALIZED VIEW IF EXISTS user_short_mv;

CREATE MATERIALIZED VIEW IF NOT EXISTS user_short_mv AS
SELECT
  e.id,
  JSONB_AGG(
    JSONB_BUILD_OBJECT(
      'locale', l.locale,
      'value',  l.value
    )
    ORDER BY l.locale
  ) AS name,
  images.url AS image
FROM users e
JOIN _user_name n ON n."B" = e.id
LEFT JOIN localizations l ON l.id = n."A"
LEFT JOIN _user_images ON _user_images."B" = e.id
LEFT JOIN LATERAL (
	SELECT id, url
	FROM images
	WHERE images.id = _user_images."A"
	ORDER BY created_at DESC
	LIMIT 1
) AS images ON true
GROUP BY e.id, images.url;

grant select on user_short_mv to commune;

select * from user_short_mv;
